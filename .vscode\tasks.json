{"version": "2.0.0", "tasks": [{"label": "🌐 启动Web管理界面", "type": "shell", "command": "python", "args": ["compute-node/web_interface.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "启动ComfyUI算力节点Web管理界面"}, {"label": "🔍 测试ComfyUI连接", "type": "shell", "command": "python", "args": ["compute-node/simple_test.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": [], "detail": "测试ComfyUI服务连接状态"}, {"label": "🎨 测试图像生成", "type": "shell", "command": "python", "args": ["compute-node/test_with_default_model.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": [], "detail": "测试ComfyUI图像生成功能"}, {"label": "🚀 启动算力节点", "type": "shell", "command": "python", "args": ["compute-node/start_comfyui_node.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "启动ComfyUI算力节点连接到调度中心"}, {"label": "📦 安装依赖", "type": "shell", "command": "pip", "args": ["install", "aiohttp", "websockets", "PyYAML"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": [], "detail": "安装ComfyUI节点所需的Python依赖包"}, {"label": "🔧 详细诊断", "type": "shell", "command": "python", "args": ["compute-node/check_comfyui_service.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": [], "detail": "运行ComfyUI服务详细诊断"}]}