#!/usr/bin/env python3
"""
启动ComfyUI算力节点
连接到Dreamify调度中心，使用ComfyUI云端算力
"""

import asyncio
import aiohttp
import json
import logging
import time
import uuid
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
import signal
import sys

from comfyui_client import ComfyUIClient

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComfyUINode:
    """ComfyUI算力节点"""
    
    def __init__(self, config_path: str = "comfyui_config.yaml"):
        self.config = self._load_config(config_path)
        self.node_id = self.config['node']['id']
        self.running = False
        self.session: Optional[aiohttp.ClientSession] = None
        self.comfyui_client: Optional[ComfyUIClient] = None
        self.active_tasks: Dict[str, asyncio.Task] = {}
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"配置文件不存在: {config_path}")
            sys.exit(1)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            sys.exit(1)
    
    async def initialize(self):
        """初始化节点"""
        logger.info(f"初始化ComfyUI节点: {self.config['node']['name']}")
        
        # 创建HTTP会话
        self.session = aiohttp.ClientSession()
        
        # 初始化ComfyUI客户端
        comfyui_url = self.config['comfyui']['server_url']
        self.comfyui_client = ComfyUIClient(comfyui_url)
        
        # 连接到ComfyUI服务
        await self.comfyui_client.connect()
        
        # 获取可用模型
        models = await self.comfyui_client.get_models()
        logger.info(f"可用模型: {len(models['checkpoints'])} 个检查点")
        
        # 注册到调度中心
        await self._register_node()
        
        self.running = True
        logger.info("ComfyUI节点初始化完成")
    
    async def _register_node(self):
        """注册节点到调度中心"""
        scheduler_url = self.config['scheduler']['url']
        register_endpoint = self.config['scheduler']['register_endpoint']
        
        # 获取ComfyUI可用模型
        models = await self.comfyui_client.get_models()
        
        registration_data = {
            "node_id": self.node_id,
            "node_name": self.config['node']['name'],
            "node_type": "comfyui",
            "server_url": self.config['comfyui']['server_url'],
            "max_concurrent_tasks": self.config['performance']['max_concurrent_tasks'],
            "supported_models": [model['name'] for model in self.config['supported_models']],
            "available_checkpoints": models['checkpoints'][:10],  # 只发送前10个
            "capabilities": {
                "max_width": 1536,
                "max_height": 1536,
                "max_steps": 50,
                "supported_formats": ["png", "jpg"],
                "features": ["txt2img", "img2img", "controlnet"]
            },
            "status": "online"
        }
        
        try:
            url = f"{scheduler_url}{register_endpoint}"
            async with self.session.post(url, json=registration_data) as response:
                if response.status == 200:
                    logger.info("节点注册成功")
                else:
                    error_text = await response.text()
                    raise RuntimeError(f"节点注册失败: {response.status} - {error_text}")
        except Exception as e:
            logger.error(f"节点注册失败: {e}")
            raise
    
    async def _send_heartbeat(self):
        """发送心跳"""
        scheduler_url = self.config['scheduler']['url']
        heartbeat_endpoint = self.config['scheduler']['heartbeat_endpoint']
        
        heartbeat_data = {
            "node_id": self.node_id,
            "status": "busy" if self.active_tasks else "online",
            "active_tasks": len(self.active_tasks),
            "max_tasks": self.config['performance']['max_concurrent_tasks'],
            "timestamp": time.time()
        }
        
        try:
            url = f"{scheduler_url}{heartbeat_endpoint}"
            async with self.session.post(url, json=heartbeat_data) as response:
                if response.status != 200:
                    logger.warning(f"心跳发送失败: {response.status}")
        except Exception as e:
            logger.error(f"心跳发送失败: {e}")
    
    async def _poll_tasks(self):
        """轮询获取新任务"""
        if len(self.active_tasks) >= self.config['performance']['max_concurrent_tasks']:
            return  # 已达到最大并发数
        
        scheduler_url = self.config['scheduler']['url']
        tasks_endpoint = self.config['scheduler']['tasks_endpoint']
        
        try:
            url = f"{scheduler_url}{tasks_endpoint}/{self.node_id}"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    tasks = data.get('tasks', [])
                    
                    for task_data in tasks:
                        if len(self.active_tasks) >= self.config['performance']['max_concurrent_tasks']:
                            break
                        await self._process_task(task_data)
        except Exception as e:
            logger.error(f"任务轮询失败: {e}")
    
    async def _process_task(self, task_data: Dict[str, Any]):
        """处理单个任务"""
        task_id = task_data['id']
        
        if task_id in self.active_tasks:
            return  # 任务已在处理中
        
        logger.info(f"开始处理任务: {task_id}")
        
        # 创建任务协程
        task_coroutine = self._execute_task(task_data)
        task = asyncio.create_task(task_coroutine)
        self.active_tasks[task_id] = task
        
        # 任务完成后清理
        def task_done_callback(task):
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
        
        task.add_done_callback(task_done_callback)
    
    async def _execute_task(self, task_data: Dict[str, Any]):
        """执行ComfyUI任务"""
        task_id = task_data['id']
        
        try:
            # 报告任务开始
            await self._report_task_status(task_id, "processing", 0)
            
            # 解析任务参数
            prompt = task_data['prompt']
            model_name = task_data.get('model', 'stable-diffusion-xl')
            width = task_data.get('width', 1024)
            height = task_data.get('height', 1024)
            steps = task_data.get('steps', 30)
            cfg_scale = task_data.get('cfg_scale', 7.5)
            seed = task_data.get('seed', -1)
            negative_prompt = task_data.get('negative_prompt', self.config['defaults']['negative_prompt'])
            
            # 查找对应的检查点模型
            checkpoint = None
            for model_config in self.config['supported_models']:
                if model_config['name'] == model_name:
                    checkpoint = model_config['checkpoint']
                    break
            
            logger.info(f"生成参数: {prompt[:50]}..., {width}x{height}, {steps}步")
            
            # 使用ComfyUI生成图像
            result = await self.comfyui_client.generate_image(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                steps=steps,
                cfg_scale=cfg_scale,
                seed=seed,
                checkpoint=checkpoint
            )
            
            # 报告任务完成
            await self._report_task_completion(task_id, result['images'], {
                'prompt_id': result.get('prompt_id'),
                'model': model_name,
                'checkpoint': checkpoint,
                'steps': steps,
                'cfg_scale': cfg_scale,
                'seed': seed
            })
            
            logger.info(f"任务完成: {task_id}")
            
        except Exception as e:
            logger.error(f"任务执行失败 {task_id}: {e}")
            await self._report_task_error(task_id, str(e))
    
    async def _report_task_status(self, task_id: str, status: str, progress: float):
        """报告任务状态"""
        scheduler_url = self.config['scheduler']['url']
        
        data = {
            'task_id': task_id,
            'status': status,
            'progress': progress,
            'node_id': self.node_id,
            'timestamp': time.time()
        }
        
        try:
            url = f"{scheduler_url}/api/tasks/{task_id}/status"
            async with self.session.post(url, json=data) as response:
                if response.status != 200:
                    logger.warning(f"状态报告失败: {response.status}")
        except Exception as e:
            logger.error(f"状态报告失败: {e}")
    
    async def _report_task_completion(self, task_id: str, images: list, metadata: dict):
        """报告任务完成"""
        scheduler_url = self.config['scheduler']['url']
        
        data = {
            'task_id': task_id,
            'status': 'completed',
            'progress': 100,
            'images': images,
            'metadata': metadata,
            'node_id': self.node_id,
            'timestamp': time.time()
        }
        
        try:
            url = f"{scheduler_url}/api/tasks/{task_id}/complete"
            async with self.session.post(url, json=data) as response:
                if response.status != 200:
                    logger.warning(f"完成报告失败: {response.status}")
        except Exception as e:
            logger.error(f"完成报告失败: {e}")
    
    async def _report_task_error(self, task_id: str, error_message: str):
        """报告任务错误"""
        scheduler_url = self.config['scheduler']['url']
        
        data = {
            'task_id': task_id,
            'status': 'failed',
            'error': error_message,
            'node_id': self.node_id,
            'timestamp': time.time()
        }
        
        try:
            url = f"{scheduler_url}/api/tasks/{task_id}/error"
            async with self.session.post(url, json=data) as response:
                if response.status != 200:
                    logger.warning(f"错误报告失败: {response.status}")
        except Exception as e:
            logger.error(f"错误报告失败: {e}")
    
    async def run(self):
        """运行节点主循环"""
        logger.info("启动ComfyUI算力节点...")
        
        try:
            await self.initialize()
            
            # 主循环
            heartbeat_interval = self.config['performance']['heartbeat_interval']
            task_check_interval = self.config['performance']['task_check_interval']
            
            last_heartbeat = 0
            
            while self.running:
                current_time = time.time()
                
                # 发送心跳
                if current_time - last_heartbeat >= heartbeat_interval:
                    await self._send_heartbeat()
                    last_heartbeat = current_time
                
                # 轮询任务
                await self._poll_tasks()
                
                # 等待下一个周期
                await asyncio.sleep(task_check_interval)
                
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭...")
        except Exception as e:
            logger.error(f"节点运行错误: {e}")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """关闭节点"""
        logger.info("正在关闭ComfyUI节点...")
        self.running = False
        
        # 等待活动任务完成
        if self.active_tasks:
            logger.info(f"等待 {len(self.active_tasks)} 个任务完成...")
            await asyncio.gather(*self.active_tasks.values(), return_exceptions=True)
        
        # 断开ComfyUI连接
        if self.comfyui_client:
            await self.comfyui_client.disconnect()
        
        # 关闭HTTP会话
        if self.session:
            await self.session.close()
        
        logger.info("ComfyUI节点已关闭")

def main():
    """主函数"""
    # 处理信号
    def signal_handler(signum, frame):
        print("\n收到退出信号，正在关闭...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并运行节点
    node = ComfyUINode()
    
    try:
        asyncio.run(node.run())
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
