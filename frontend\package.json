{"name": "dreamify-frontend", "version": "1.0.0", "description": "Dreamify AI绘画平台前端应用", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.2", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "socket.io-client": "^4.7.4", "axios": "^1.6.0", "swr": "^2.2.4", "zustand": "^4.4.6", "immer": "^10.0.3", "react-dropzone": "^14.2.3", "react-image-gallery": "^1.3.0", "react-intersection-observer": "^9.5.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "date-fns": "^2.30.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "tailwind-merge": "^2.0.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7"}, "devDependencies": {"eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "jest-environment-jsdom": "^29.7.0", "@types/react-image-gallery": "^1.2.4", "@types/react-window": "^1.8.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}