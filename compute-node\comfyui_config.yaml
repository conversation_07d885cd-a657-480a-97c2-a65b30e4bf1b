# ComfyUI云端算力配置文件

# ComfyUI服务配置
comfyui:
  server_url: "https://koomfonjr8-8188.cnb.run"
  client_id: ""  # 留空自动生成
  timeout: 300   # 任务超时时间(秒)

# 节点基本信息
node:
  id: "comfyui-node-001"
  name: "ComfyUI云端节点"
  type: "comfyui"
  description: "基于ComfyUI的云端算力节点"

# 调度中心配置
scheduler:
  url: "http://localhost:8001"  # 本地调度中心地址
  register_endpoint: "/api/nodes/register"
  heartbeat_endpoint: "/api/nodes/heartbeat"
  tasks_endpoint: "/api/nodes/tasks"

# 性能配置
performance:
  max_concurrent_tasks: 2      # 最大并发任务数
  heartbeat_interval: 30       # 心跳间隔(秒)
  task_check_interval: 5       # 任务检查间隔(秒)
  max_retries: 3              # 最大重试次数

# 支持的模型和参数
supported_models:
  - name: "stable-diffusion-xl"
    checkpoint: "sd_xl_base_1.0.safetensors"
    max_width: 1536
    max_height: 1536
    max_steps: 50
    default_steps: 30
  
  - name: "stable-diffusion-v1-5"
    checkpoint: "v1-5-pruned-emaonly.ckpt"
    max_width: 1024
    max_height: 1024
    max_steps: 50
    default_steps: 20

# 默认生成参数
defaults:
  width: 1024
  height: 1024
  steps: 30
  cfg_scale: 7.5
  sampler: "euler"
  scheduler: "normal"
  negative_prompt: "blurry, low quality, distorted, deformed"

# 日志配置
logging:
  level: "INFO"
  file: "./logs/comfyui_node.log"
  max_size: "10MB"
  backup_count: 5

# 临时文件配置
temp:
  directory: "./temp"
  cleanup_interval: 3600  # 清理间隔(秒)
  max_age: 86400         # 文件最大保存时间(秒)
