<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络剪切板</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📋</text></svg>">

    <!-- PWA 支持 -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="description" content="功能强大的网络剪切板，支持AI智能分类、数据可视化、多主题等">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="剪切板">
    <link rel="manifest" href="#" id="manifest-placeholder">
    <link rel="apple-touch-icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNDAiIGhlaWdodD0iMjQwIj48cmVjdCB3aWR0aD0iMjQwIiBoZWlnaHQ9IjI0MCIgZmlsbD0iIzNiODJmNiIvPjx0ZXh0IHg9IjEyMCIgeT0iMTIwIiBmaWxsPSIjZmZmZmZmIiBmb250LXNpemU9IjI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7wn5CyPC90ZXh0Pjwvc3ZnPg==">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 🎨 优化的CSS变量系统 */
        :root {
            /* 主色调 - 优雅蓝色系 */
            --primary-blue: #2563eb;
            --primary-blue-light: #3b82f6;
            --primary-blue-dark: #1d4ed8;
            --secondary-blue: #0ea5e9;
            --accent-blue: #06b6d4;

            /* 中性色调 */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            /* 功能色调 */
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: var(--secondary-blue);

            /* 浅色主题变量 */
            --bg-primary: linear-gradient(135deg, var(--gray-50) 0%, #e0f2fe 100%);
            --bg-secondary: var(--primary-blue);
            --bg-tertiary: var(--secondary-blue);
            --bg-quaternary: var(--accent-blue);
            --card-bg: rgba(255, 255, 255, 0.9);
            --text-color: var(--gray-800);
            --text-medium: var(--gray-600);
            --text-dim: var(--gray-500);
            --text-light: white;
            --border-color: rgba(37, 99, 235, 0.2);
            --card-border: rgba(148, 163, 184, 0.15);
            --bg-glass: rgba(255, 255, 255, 0.8);
            --bg-glass-hover: rgba(255, 255, 255, 0.95);
            --highlight-color: var(--primary-blue);
            --gradient-text: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);

            /* 按钮颜色 */
            --button-primary: var(--primary-blue);
            --button-secondary: var(--gray-600);
            --button-success: var(--success);
            --button-warning: var(--warning);
            --button-danger: var(--danger);
            --button-info: var(--info);

            /* 阴影和效果 */
            --shadow: 0 2px 8px rgba(15, 23, 42, 0.08);
            --shadow-hover: 0 4px 16px rgba(15, 23, 42, 0.12);
            --shadow-glow: 0 0 20px rgba(37, 99, 235, 0.3);
            --backdrop-blur: blur(12px);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --border-radius: 12px;
            --button-radius: 8px;
            --card-padding: 20px;
            --glass-border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 🌟 全局样式重置 */
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* 🌌 现代化渐变背景 */
        body {
            background: var(--bg-primary);
            background-attachment: fixed;
            padding: 20px;
            margin: 0;
            max-width: 1200px;
            margin: 0 auto;
            color: var(--text-color);
            min-height: 100vh;
            font-size: 16px;
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }

        /* 简洁背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(14, 165, 233, 0.04) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.03) 0%, transparent 70%);
            z-index: -1;
        }

        /* 🎨 简洁优雅标题 - 紧凑版 */
        h1 {
            text-align: left;
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
            background: var(--gradient-text);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.01em;
            position: relative;
            flex: 1;
        }

        /* 移除标题装饰线以节省空间 */

        /* 🎪 优化的头部区域 - 改为一行显示 */
        .header {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            gap: 15px;
            position: relative;
            z-index: 10;
            flex-wrap: wrap;
        }

        /* 🌙 优化的切换按钮 */
        .dark-mode-toggle {
            background: var(--bg-glass);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            transition: var(--transition);
            box-shadow: var(--shadow);
            backdrop-filter: var(--backdrop-blur);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .dark-mode-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-blue);
            opacity: 0;
            transition: var(--transition);
            border-radius: 50%;
        }

        .dark-mode-toggle:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: var(--shadow-hover);
            border-color: var(--primary-blue);
        }

        .dark-mode-toggle:hover::before {
            opacity: 0.1;
        }

        .dark-mode-toggle:hover span {
            color: var(--primary-blue);
            z-index: 1;
            position: relative;
        }

        /* 🎨 现代化简洁卡片样式 */
        .snippet {
            margin-bottom: 16px;
            padding: 18px;
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: var(--border-radius);
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            transition: var(--transition);
            box-shadow: var(--shadow);
            font-size: 15px;
            line-height: 1.5;
            backdrop-filter: var(--backdrop-blur);
            position: relative;
            overflow: hidden;
            cursor: grab;
        }

        /* 🎯 拖拽状态样式 */
        .snippet.dragging {
            opacity: 0.6;
            transform: rotate(2deg) scale(1.02);
            cursor: grabbing;
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2);
        }

        .snippet.drag-over {
            border-color: var(--primary-blue);
            background: rgba(37, 99, 235, 0.05);
            transform: translateY(-1px);
        }

        .drag-handle {
            cursor: grab;
            padding: 6px;
            margin-right: 12px;
            color: var(--text-dim);
            font-size: 16px;
            transition: var(--transition);
            border-radius: 6px;
        }

        .drag-handle:hover {
            color: var(--primary-blue);
            background: rgba(37, 99, 235, 0.08);
        }

        .drag-handle:active {
            cursor: grabbing;
        }



        .snippet:hover {
            border-color: var(--primary-blue);
            box-shadow: var(--shadow-hover);
            background: var(--bg-glass-hover);
            transform: translateY(-1px);
        }

        .snippet-content {
            flex-grow: 1;
            text-align: left;
            padding: 12px;
            word-break: break-word;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s ease;
            position: relative;
        }

        .snippet-content:hover {
            background: rgba(37, 99, 235, 0.04);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
        }

        .snippet-content:hover::after {
            content: "点击智能复制";
            position: absolute;
            top: 6px;
            right: 10px;
            font-size: 11px;
            color: var(--primary-blue);
            background: rgba(37, 99, 235, 0.1);
            padding: 3px 8px;
            border-radius: 6px;
            opacity: 0.9;
            pointer-events: none;
            z-index: 10;
            font-weight: 500;
        }

        .snippet-content:active {
            transform: translateY(0);
            background: rgba(37, 99, 235, 0.08);
        }

        .actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* 统一按钮基础样式 */
        .copy-button, .delete-button, .edit-button, .pin-button, .toggle-expand-button,
        .add-button, .add-section-btn, .toggle-all-expand-btn,
        .export-button, .import-button, .batch-controls button, .sort-options button,
        .search-container button, .advanced-toggle-btn {
            padding: 8px 14px;
            border: none;
            border-radius: var(--button-radius);
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: var(--shadow);
            min-height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            background: var(--bg-glass);
            color: var(--text-color);
            border: 1px solid var(--card-border);
        }

        /* 通用按钮悬停效果 */
        .copy-button:hover, .delete-button:hover, .edit-button:hover, .pin-button:hover,
        .toggle-expand-button:hover, .add-button:hover, .add-section-btn:hover,
        .sort-options button:hover, .search-container button:hover,
        .advanced-toggle-btn:hover, .export-button:hover, .import-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
        }

        .add-section-input {
            flex-grow: 1;
            padding: 6px 10px;
            border: 1px solid var(--card-border);
            border-radius: 6px;
            font-size: 13px;
        }

        .add-section-input, #searchInput, .section-selector {
            padding: 10px 14px;
            border: 1px solid var(--card-border);
            border-radius: var(--button-radius);
            font-size: 14px;
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            transition: all 0.3s ease;
            min-height: 36px;
            box-sizing: border-box;
            color: var(--text-color);
        }

        .add-section-input:focus, #searchInput:focus, .section-selector:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            background: var(--bg-glass-hover);
        }

        .toggle-all-expand-btn {
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--button-radius);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-all-expand-btn:hover {
            background: var(--primary-blue-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }

        /* 简洁标签样式 */
        .section-tab {
            padding: 8px 16px;
            background: rgba(37, 99, 235, 0.08);
            color: var(--primary-blue);
            border: 1px solid rgba(37, 99, 235, 0.2);
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-tab:hover {
            background: rgba(37, 99, 235, 0.12);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .section-tab.active {
            background: var(--primary-blue);
            color: white;
            box-shadow: var(--shadow);
        }

        .add-section-container {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            align-items: center;
        }

        .add-section-btn {
            padding: 10px 16px;
            background: var(--button-success);
            color: white;
            border: none;
            border-radius: var(--button-radius);
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            min-height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
        }

        .usage-tips {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 16px;
            background: rgba(37, 99, 235, 0.06);
            border-radius: 16px;
            color: var(--primary-blue);
            font-size: 12px;
            max-width: 320px;
            margin-left: auto;
            margin-right: auto;
            border: 1px solid rgba(37, 99, 235, 0.1);
        }

        .compact-tips {
            margin: 10px 0;
            padding: 10px 14px;
            background: rgba(37, 99, 235, 0.04);
            border-radius: 8px;
            font-size: 12px;
            color: var(--text-medium);
            line-height: 1.4;
            border: 1px solid rgba(37, 99, 235, 0.08);
        }

        .batch-management {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid var(--card-border);
        }

        .batch-management h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .batch-controls {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-wrap: wrap;
        }

        #batchCounter {
            color: var(--text-medium);
            font-size: 11px;
            white-space: nowrap;
        }

        .expand-control-container {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
        }

        .toggle-all-expand-btn {
            background: linear-gradient(135deg, #007aff, #5856d6);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--button-radius);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-area, .search-container, .sort-options, .export-import-container {
            margin: 20px auto;
            width: 90%;
            text-align: center;
        }

        textarea {
            width: 90%;
            padding: 16px;
            margin-bottom: 20px;
            border: 1px solid var(--card-border);
            border-radius: var(--border-radius);
            font-size: 15px;
            resize: vertical;
            min-height: 120px;
            background: var(--bg-glass);
            backdrop-filter: var(--backdrop-blur);
            transition: var(--transition);
            font-family: inherit;
            line-height: 1.6;
            box-shadow: var(--shadow);
            color: var(--text-color);
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), var(--shadow-hover);
            background: var(--bg-glass-hover);
            transform: translateY(-1px);
        }

        textarea::placeholder {
            color: var(--text-dim);
            font-style: italic;
        }

        .add-button {
            padding: 12px 24px;
            background-color: var(--button-primary);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            cursor: pointer;
        }

        .add-button {
            background: linear-gradient(135deg, var(--button-primary), #0066cc);
            color: white;
            font-size: 15px;
        }

        .add-button:hover {
            background: linear-gradient(135deg, #0066cc, #0056b3);
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(0, 122, 255, 0.3);
        }

        .advanced-features {
            display: none;
        }

        .tags-input-container {
            margin-bottom: 10px;
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 10px;
        }

        .tag {
            display: inline-block;
            background: rgba(0, 123, 255, 0.1);
            color: var(--highlight-color);
            padding: 4px 12px;
            border-radius: 20px;
            margin: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .dark-mode {
            --bg-color: #1a1a1a;
            --text-color: #f5f5f7;
            --card-bg: rgba(45, 45, 47, 0.8);
            --border-color: rgba(255, 255, 255, 0.1);
            --card-border: rgba(255, 255, 255, 0.05);
        }

        .dark-mode body {
            background: linear-gradient(135deg, #1a1a1a, #2d2d30);
        }

        /* 分区选择器样式 */
        .section-selector {
            padding: 4px 8px;
            border-radius: 6px;
            border: 1px solid var(--card-border);
            font-size: 12px;
            background: var(--card-bg);
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .section-selector:hover {
            border-color: var(--highlight-color);
        }

        .section-selector:focus {
            outline: none;
            border-color: var(--highlight-color);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
        }

        /* 高亮被复制的文本片段 */
        .highlighted-text {
            background: linear-gradient(90deg, rgba(0, 123, 255, 0.3), rgba(0, 123, 255, 0.1));
            color: var(--highlight-color);
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: 600;
            box-shadow: 0 1px 3px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
        }

        /* 悬停时的文本片段高亮 */
        .hover-segment {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
            padding: 1px 3px;
            border-radius: 3px;
            transition: all 0.15s ease;
            cursor: pointer;
        }

        /* 🎨 简洁现代按钮样式 */
        .copy-button {
            background: var(--button-success) !important;
            color: white !important;
            border: none !important;
            font-weight: 600 !important;
            box-shadow: var(--shadow) !important;
        }

        .copy-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .delete-button {
            background: var(--button-danger) !important;
            color: white !important;
            border: none !important;
            font-weight: 600 !important;
            box-shadow: var(--shadow) !important;
        }

        .delete-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .edit-button {
            background: var(--button-warning) !important;
            color: white !important;
            border: none !important;
            font-weight: 600 !important;
            box-shadow: var(--shadow) !important;
        }

        .edit-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .pin-button {
            background: var(--button-info) !important;
            color: white !important;
            width: 40px;
            height: 40px;
            padding: 0 !important;
            min-height: 40px;
            border: none !important;
        }

        .pin-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .toggle-expand-button {
            background: var(--button-secondary) !important;
            color: white !important;
            border: none !important;
            font-weight: 600 !important;
            box-shadow: var(--shadow) !important;
        }

        .toggle-expand-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .add-button {
            background: var(--button-primary) !important;
            color: white !important;
            border: none !important;
        }

        .add-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }

        .add-section-btn {
            background: var(--button-success) !important;
            color: white !important;
            border: none !important;
        }

        .add-section-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover) !important;
        }



        /* 功能面板内部布局修复 */
        .collapsible-content {
            overflow: hidden;
            transition: max-height 0.3s ease, opacity 0.3s ease;
        }

        .collapsible-content.collapsed {
            max-height: 0 !important;
            opacity: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
            visibility: hidden !important;
        }

        .collapsible-content.collapsed * {
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            visibility: hidden !important;
        }

        .collapsible-content.collapsed * {
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
        }

        .collapsible-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            margin-bottom: 10px;
            padding: 5px 0;
        }

        .collapsible-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
        }

        .collapsible-toggle {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            transition: transform 0.3s ease;
            padding: 4px;
            border-radius: 4px;
        }

        .collapsible-toggle:hover {
            background: rgba(0, 123, 255, 0.1);
        }

        .collapsible-toggle.collapsed {
            transform: rotate(-90deg);
        }

        /* 分区管理样式 */
        .section-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--card-border);
        }

        .add-section-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }

        .add-section-input {
            flex-grow: 1;
        }

        /* 批量管理样式 */
        .batch-management {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid var(--card-border);
        }

        .batch-management h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .batch-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        #batchActions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        #batchCounter {
            color: var(--text-medium);
            font-size: 12px;
            white-space: nowrap;
        }

        /* 搜索容器样式 */
        .search-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-container input {
            flex-grow: 1;
        }

        /* 排序选项样式 */
        .sort-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .expand-control-container {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
        }

        .compact-tips {
            margin: 10px 0;
            padding: 8px 12px;
            background: rgba(74, 144, 226, 0.08);
            border-radius: 8px;
            font-size: 12px;
            color: var(--text-medium);
            line-height: 1.4;
        }

        /* 🌟 简洁置顶样式 */
        .snippet.pinned {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(16, 185, 129, 0.1)) !important;
            border: 2px solid rgba(59, 130, 246, 0.4) !important;
            position: relative;
        }

        /* 🌟 简洁置顶按钮状态 */
        .pin-button.pinned {
            background: var(--bg-secondary) !important;
            color: white !important;
            box-shadow: var(--shadow-glow) !important;
        }

        .pin-button.pinned:hover {
            transform: translateY(-1px);
        }

        /* 🎪 简化的动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInUp {
            from {
                transform: translateY(10px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes gentleGlow {
            0%, 100% { box-shadow: 0 0 8px rgba(37, 99, 235, 0.2); }
            50% { box-shadow: 0 0 16px rgba(37, 99, 235, 0.4); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideInLeft {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 🌟 简化的页面加载动画 */
        .snippet {
            animation: slideInUp 0.3s ease-out;
        }

        .header {
            animation: fadeIn 0.5s ease-out;
        }

        .section-tab {
            animation: fadeInScale 0.2s ease-out;
        }

        .section-tab:nth-child(2) { animation-delay: 0.05s; }
        .section-tab:nth-child(3) { animation-delay: 0.1s; }
        .section-tab:nth-child(4) { animation-delay: 0.15s; }
        .section-tab:nth-child(5) { animation-delay: 0.2s; }

        /* 搜索高亮动画 */
        .search-highlight {
            animation: gentleGlow 1.5s ease-in-out infinite;
        }

        /* 录音指示器动画 */
        .recording-dot {
            animation: pulse 1s infinite;
        }

        /* 🎨 优化的主题系统 */
        .theme-forest {
            --bg-primary: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            --bg-secondary: #10b981;
            --bg-tertiary: #059669;
            --bg-quaternary: #047857;
            --card-bg: rgba(255, 255, 255, 0.95);
            --text-color: #065f46;
            --text-medium: #047857;
            --text-dim: #059669;
            --border-color: rgba(16, 185, 129, 0.2);
            --card-border: rgba(5, 150, 105, 0.15);
            --bg-glass: rgba(255, 255, 255, 0.9);
            --bg-glass-hover: rgba(255, 255, 255, 0.95);
            --highlight-color: #10b981;
            --primary-blue: #10b981;
            --primary-blue-light: #34d399;
            --primary-blue-dark: #059669;
        }

        .theme-ocean {
            --bg-primary: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            --bg-secondary: #0ea5e9;
            --bg-tertiary: #0284c7;
            --bg-quaternary: #0369a1;
            --card-bg: rgba(255, 255, 255, 0.95);
            --text-color: #0c4a6e;
            --text-medium: #0369a1;
            --text-dim: #0284c7;
            --border-color: rgba(14, 165, 233, 0.2);
            --card-border: rgba(2, 132, 199, 0.15);
            --bg-glass: rgba(255, 255, 255, 0.9);
            --bg-glass-hover: rgba(255, 255, 255, 0.95);
            --highlight-color: #0ea5e9;
            --primary-blue: #0ea5e9;
            --primary-blue-light: #38bdf8;
            --primary-blue-dark: #0284c7;
        }

        .theme-purple {
            --bg-primary: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
            --bg-secondary: #8b5cf6;
            --bg-tertiary: #7c3aed;
            --bg-quaternary: #6d28d9;
            --card-bg: rgba(255, 255, 255, 0.95);
            --text-color: #581c87;
            --text-medium: #6d28d9;
            --text-dim: #7c3aed;
            --border-color: rgba(139, 92, 246, 0.2);
            --card-border: rgba(124, 58, 237, 0.15);
            --bg-glass: rgba(255, 255, 255, 0.9);
            --bg-glass-hover: rgba(255, 255, 255, 0.95);
            --highlight-color: #8b5cf6;
            --primary-blue: #8b5cf6;
            --primary-blue-light: #a78bfa;
            --primary-blue-dark: #7c3aed;
        }

        .theme-rose {
            --bg-primary: linear-gradient(135deg, #fff1f2 0%, #fce7f3 100%);
            --bg-secondary: #ec4899;
            --bg-tertiary: #db2777;
            --bg-quaternary: #be185d;
            --card-bg: rgba(255, 255, 255, 0.95);
            --text-color: #9f1239;
            --text-medium: #be185d;
            --text-dim: #db2777;
            --border-color: rgba(236, 72, 153, 0.2);
            --card-border: rgba(219, 39, 119, 0.15);
            --bg-glass: rgba(255, 255, 255, 0.9);
            --bg-glass-hover: rgba(255, 255, 255, 0.95);
            --highlight-color: #ec4899;
            --primary-blue: #ec4899;
            --primary-blue-light: #f472b6;
            --primary-blue-dark: #db2777;
        }

        /* 🌙 优化的深色模式 */
        .dark-mode {
            --bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            --bg-secondary: #3b82f6;
            --bg-tertiary: #1d4ed8;
            --bg-quaternary: #1e40af;
            --card-bg: rgba(30, 41, 59, 0.95);
            --text-color: #f1f5f9;
            --text-medium: #cbd5e1;
            --text-dim: #94a3b8;
            --text-light: #ffffff;
            --border-color: rgba(59, 130, 246, 0.3);
            --card-border: rgba(148, 163, 184, 0.15);
            --bg-glass: rgba(30, 41, 59, 0.9);
            --bg-glass-hover: rgba(30, 41, 59, 0.95);
            --highlight-color: #60a5fa;
            --primary-blue: #60a5fa;
            --primary-blue-light: #93c5fd;
            --primary-blue-dark: #3b82f6;
            --gradient-text: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);

            /* 功能色调 */
            --success: #34d399;
            --warning: #fbbf24;
            --danger: #f87171;
            --info: #60a5fa;

            /* 按钮颜色 */
            --button-primary: #60a5fa;
            --button-secondary: #6b7280;
            --button-success: #34d399;
            --button-warning: #fbbf24;
            --button-danger: #f87171;
            --button-info: #60a5fa;
        }

        /* 主题选择器样式 */
        .theme-option:hover {
            border-color: var(--highlight-color) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .theme-option.active {
            border-color: var(--highlight-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        /* 🎵 多媒体增强样式 */
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
            position: relative;
        }

        .code-block::before {
            content: attr(data-language);
            position: absolute;
            top: 5px;
            right: 10px;
            font-size: 11px;
            color: #6c757d;
            background: rgba(255, 255, 255, 0.8);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .code-keyword { color: #d73a49; font-weight: bold; }
        .code-string { color: #032f62; }
        .code-comment { color: #6a737d; font-style: italic; }
        .code-number { color: #005cc5; }
        .code-function { color: #6f42c1; }

        .file-preview {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }

        .file-preview-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .file-icon {
            font-size: 24px;
        }

        .file-info {
            flex-grow: 1;
        }

        .file-name {
            font-weight: 600;
            color: var(--text-color);
        }

        .file-size {
            font-size: 12px;
            color: var(--text-medium);
        }

        .audio-player {
            width: 100%;
            margin: 10px 0;
            border-radius: 8px;
        }

        .media-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-top: 10px;
        }

        .media-button {
            padding: 8px 12px;
            background: var(--button-primary);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: var(--transition);
        }

        .media-button:hover {
            background: var(--button-secondary);
            transform: translateY(-1px);
        }

        .recording-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            color: #dc3545;
            font-size: 12px;
        }

        .recording-dot {
            width: 8px;
            height: 8px;
            background: #dc3545;
            border-radius: 50%;
            animation: pulse 1s infinite;
        }

        /* 👆 手势操作样式 */
        .swipe-action {
            position: relative;
            overflow: hidden;
        }

        .swipe-background {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            background: linear-gradient(90deg, transparent, #dc3545);
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 20px;
            color: white;
            font-weight: bold;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .swipe-background.active {
            transform: translateX(0);
        }

        .snippet.swiping {
            transform: translateX(-80px);
            transition: transform 0.3s ease;
        }

        .snippet.double-tap-highlight {
            background: rgba(59, 130, 246, 0.1) !important;
            border-color: var(--highlight-color) !important;
            animation: glow 0.5s ease-out;
        }

        .long-press-menu {
            position: absolute;
            background: var(--card-bg);
            border: var(--glass-border);
            border-radius: 8px;
            padding: 10px;
            box-shadow: var(--shadow-hover);
            z-index: 1000;
            display: none;
        }

        .long-press-menu button {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin: 2px 0;
            background: none;
            border: none;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            transition: var(--transition);
        }

        .long-press-menu button:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        .dark-mode body {
            background: var(--bg-primary);
        }

        .dark-mode body::before {
            background:
                radial-gradient(circle at 20% 80%, rgba(96, 165, 250, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(37, 99, 235, 0.04) 0%, transparent 70%);
        }

        .dark-mode .snippet {
            background: var(--card-bg);
            border-color: var(--card-border);
            color: var(--text-color);
        }

        .dark-mode .snippet:hover {
            border-color: var(--primary-blue);
            box-shadow: var(--shadow-hover);
            background: var(--bg-glass-hover);
        }

        .dark-mode h1 {
            background: var(--gradient-text);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 📱 优化的响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 12px;
                font-size: 14px;
                max-width: 100%;
            }

            h1 {
                font-size: 1.5rem;
                margin-bottom: 0;
            }

            .header {
                gap: 10px;
                margin-bottom: 15px;
                flex-direction: column;
                align-items: stretch;
            }

            .header > div:first-child {
                text-align: center;
                margin-bottom: 10px;
            }

            .header > div:last-child {
                justify-content: center;
            }

            .snippet {
                padding: 14px;
                margin-bottom: 12px;
                flex-direction: column;
                align-items: stretch;
            }

            .snippet-content {
                padding: 10px;
                margin-bottom: 10px;
                order: 1;
                min-height: 44px; /* 符合触摸目标最小尺寸 */
            }

            .drag-handle {
                order: 0;
                align-self: flex-start;
                margin-bottom: 8px;
                margin-right: 0;
            }

            .snippet .actions {
                order: 2;
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
                justify-content: flex-start;
            }

            .copy-button, .delete-button, .edit-button, .pin-button, .toggle-expand-button {
                padding: 6px 10px;
                font-size: 11px;
                min-height: 32px;
                flex: 1;
                min-width: 60px;
            }

            textarea {
                width: 100%;
                padding: 12px;
                font-size: 14px;
                min-height: 100px;
            }

            .add-button {
                padding: 10px 18px;
                font-size: 14px;
                width: 100%;
            }

            .dark-mode-toggle {
                width: 44px;
                height: 44px;
                font-size: 18px;
            }

            .sort-options, .batch-controls {
                justify-content: center;
                flex-wrap: wrap;
                gap: 6px;
            }

            .search-container {
                flex-direction: column;
                gap: 8px;
            }

            /* 移动端分页控制优化 */
            .pagination-controls {
                padding: 10px;
            }

            .pagination-controls button {
                padding: 10px 16px;
                font-size: 14px;
                min-height: 44px;
            }

            /* 主题选择器优化 */
            .theme-selector {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .theme-option {
                padding: 10px;
                font-size: 12px;
            }

            /* 数据可视化优化 */
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .chart-container {
                height: 180px;
            }
        }

        /* 📱 小屏幕设备进一步优化 */
        @media (max-width: 480px) {
            body {
                padding: 8px;
            }

            h1 {
                font-size: 1.6rem;
            }

            .snippet {
                padding: 12px;
            }

            .theme-selector {
                grid-template-columns: 1fr;
            }

            .copy-button, .delete-button, .edit-button, .pin-button {
                font-size: 10px;
                padding: 5px 8px;
                min-height: 36px;
            }

            .chart-container {
                height: 150px;
            }

            .dark-mode-toggle {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        /* 🎯 可访问性样式 */

        /* 跳转链接 */
        .skip-links {
            position: absolute;
            top: -40px;
            left: 6px;
            z-index: 10000;
        }

        .skip-link {
            position: absolute;
            left: -10000px;
            top: auto;
            width: 1px;
            height: 1px;
            overflow: hidden;
            background: var(--primary-blue);
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 600;
        }

        .skip-link:focus {
            position: static;
            width: auto;
            height: auto;
            left: auto;
            top: auto;
            overflow: visible;
        }

        /* 高对比度模式 */
        .high-contrast {
            --bg-primary: #000000;
            --bg-secondary: #ffffff;
            --card-bg: #ffffff;
            --text-color: #000000;
            --text-medium: #333333;
            --text-dim: #666666;
            --text-light: #000000;
            --border-color: #000000;
            --card-border: #000000;
            --bg-glass: #ffffff;
            --bg-glass-hover: #f0f0f0;
            --primary-blue: #0000ff;
            --primary-blue-light: #3333ff;
            --primary-blue-dark: #000099;
            --success: #008000;
            --warning: #ff8000;
            --danger: #ff0000;
            --info: #0000ff;
            --button-primary: #0000ff;
            --button-secondary: #666666;
            --button-success: #008000;
            --button-warning: #ff8000;
            --button-danger: #ff0000;
            --button-info: #0000ff;
        }

        .high-contrast body {
            background: #ffffff;
            color: #000000;
        }

        .high-contrast .snippet {
            background: #ffffff;
            border: 2px solid #000000;
            color: #000000;
        }

        .high-contrast .snippet:hover {
            background: #f0f0f0;
            border-color: #0000ff;
        }

        .high-contrast button {
            background: #ffffff;
            color: #000000;
            border: 2px solid #000000;
        }

        .high-contrast button:hover {
            background: #000000;
            color: #ffffff;
        }

        .high-contrast .copy-button {
            background: #0000ff;
            color: #ffffff;
        }

        .high-contrast .delete-button {
            background: #ff0000;
            color: #ffffff;
        }

        .high-contrast .edit-button {
            background: #008000;
            color: #ffffff;
        }

        .high-contrast input,
        .high-contrast textarea,
        .high-contrast select {
            background: #ffffff;
            color: #000000;
            border: 2px solid #000000;
        }

        .high-contrast input:focus,
        .high-contrast textarea:focus,
        .high-contrast select:focus {
            outline: 3px solid #0000ff;
            outline-offset: 2px;
        }

        /* 焦点指示器增强 */
        *:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        .high-contrast *:focus {
            outline: 3px solid #0000ff;
            outline-offset: 2px;
        }

        /* 键盘导航指示器 */
        .keyboard-focused {
            outline: 3px solid var(--primary-blue);
            outline-offset: 2px;
            box-shadow: 0 0 0 5px rgba(37, 99, 235, 0.2);
        }

        .high-contrast .keyboard-focused {
            outline: 3px solid #0000ff;
            box-shadow: 0 0 0 5px rgba(0, 0, 255, 0.3);
        }

        /* ⌨️ 键盘快捷键提示 */
        .keyboard-hint {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--card-bg);
            border: var(--glass-border);
            border-radius: var(--border-radius);
            padding: 10px 15px;
            font-size: 12px;
            color: var(--text-medium);
            box-shadow: var(--shadow);
            backdrop-filter: var(--backdrop-blur);
            opacity: 0;
            transform: translateY(20px);
            transition: var(--transition);
            z-index: 1000;
        }

        .keyboard-hint.show {
            opacity: 1;
            transform: translateY(0);
        }

        .keyboard-hint kbd {
            background: var(--highlight-color);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <!-- 🌟 紧凑的头部区域 - 一行显示 -->
    <div class="header">
        <div style="display: flex; flex-direction: column; flex: 1;">
            <h1>🌟 超级美化网络剪切板</h1>
            <p style="margin: 5px 0 0 0; font-size: 0.9rem; color: var(--text-medium); font-weight: 500;">
                ✨ 云端智能管理 · 让数据更有序 ✨
            </p>
        </div>
        <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
            <button id="darkModeToggle" class="dark-mode-toggle" onclick="toggleDarkMode()" title="切换深色模式" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 45px; height: 45px; cursor: pointer; font-size: 20px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span id="darkModeIcon">🌙</span>
            </button>
            <button id="themeToggle" class="dark-mode-toggle" onclick="showThemeSelector()" title="主题选择" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 45px; height: 45px; cursor: pointer; font-size: 20px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span>🎨</span>
            </button>
            <button id="dataRecoveryBtn" class="dark-mode-toggle" onclick="showDataRecovery()" title="数据恢复" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 45px; height: 45px; cursor: pointer; font-size: 20px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span>🔧</span>
            </button>
            <button class="dark-mode-toggle" onclick="showStats()" title="统计信息" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 45px; height: 45px; cursor: pointer; font-size: 20px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span>📊</span>
            </button>
            <button class="dark-mode-toggle" onclick="showAIPanel()" title="AI助手" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 45px; height: 45px; cursor: pointer; font-size: 20px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span>🤖</span>
            </button>
            <button class="dark-mode-toggle" onclick="showDataVisualization()" title="数据可视化" style="background: var(--bg-glass); border: var(--glass-border); border-radius: 50%; width: 45px; height: 45px; cursor: pointer; font-size: 20px; transition: var(--transition); box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); display: flex; align-items: center; justify-content: center;">
                <span>📈</span>
            </button>
        </div>
    </div>

    <!-- 🎨 主题选择面板 -->
    <div id="themePanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 90%; text-align: center; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600;">🎨 主题选择</h3>
        <p style="color: var(--text-medium); margin-bottom: 20px; line-height: 1.5;">选择您喜欢的颜色主题：</p>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin-bottom: 20px;">
            <div class="theme-option" data-theme="default" onclick="applyTheme('default')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
                <div style="color: #1e293b; font-weight: 600; margin-bottom: 5px;">🌟 默认</div>
                <div style="font-size: 12px; color: #64748b;">清爽蓝色</div>
            </div>

            <div class="theme-option" data-theme="ocean" onclick="applyTheme('ocean')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #f0f9ff 0%, #0ea5e9 100%);">
                <div style="color: #0c4a6e; font-weight: 600; margin-bottom: 5px;">🌊 海洋</div>
                <div style="font-size: 12px; color: #0369a1;">深邃蓝色</div>
            </div>
            <div class="theme-option" data-theme="forest" onclick="applyTheme('forest')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #ecfdf5 0%, #10b981 100%);">
                <div style="color: #065f46; font-weight: 600; margin-bottom: 5px;">🌲 森林</div>
                <div style="font-size: 12px; color: #047857;">自然绿色</div>
            </div>
            <div class="theme-option" data-theme="purple" onclick="applyTheme('purple')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #faf5ff 0%, #8b5cf6 100%);">
                <div style="color: #581c87; font-weight: 600; margin-bottom: 5px;">💜 紫色</div>
                <div style="font-size: 12px; color: #6d28d9;">优雅紫色</div>
            </div>
            <div class="theme-option" data-theme="rose" onclick="applyTheme('rose')" style="cursor: pointer; padding: 15px; border-radius: 12px; border: 2px solid transparent; transition: all 0.3s ease; background: linear-gradient(135deg, #fff1f2 0%, #ec4899 100%);">
                <div style="color: #9f1239; font-weight: 600; margin-bottom: 5px;">🌹 玫瑰</div>
                <div style="font-size: 12px; color: #be185d;">浪漫粉色</div>
            </div>
        </div>
        <button onclick="hideThemeSelector()" style="padding: 8px 16px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
    </div>

    <!-- 🤖 AI智能助手面板 -->
    <div id="aiPanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 90%; text-align: center; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600;">🤖 AI智能助手</h3>
        <p style="color: var(--text-medium); margin-bottom: 20px; line-height: 1.5;">让AI帮你更好地管理内容：</p>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
            <div style="background: rgba(59, 130, 246, 0.1); padding: 15px; border-radius: 12px; border: 1px solid rgba(59, 130, 246, 0.2);">
                <div style="font-size: 24px; margin-bottom: 8px;">🏷️</div>
                <div style="font-weight: 600; margin-bottom: 5px; color: var(--text-color);">智能分类</div>
                <div style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">自动为内容分类</div>
                <button onclick="runAutoClassification()" style="padding: 8px 16px; background: var(--button-primary); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">开始分类</button>
            </div>

            <div style="background: rgba(16, 185, 129, 0.1); padding: 15px; border-radius: 12px; border: 1px solid rgba(16, 185, 129, 0.2);">
                <div style="font-size: 24px; margin-bottom: 8px;">🔍</div>
                <div style="font-weight: 600; margin-bottom: 5px; color: var(--text-color);">重复检测</div>
                <div style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">找出重复内容</div>
                <button onclick="findDuplicates()" style="padding: 8px 16px; background: var(--button-success); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">检测重复</button>
            </div>

            <div style="background: rgba(37, 99, 235, 0.1); padding: 15px; border-radius: 12px; border: 1px solid rgba(37, 99, 235, 0.2);">
                <div style="font-size: 24px; margin-bottom: 8px;">✨</div>
                <div style="font-weight: 600; margin-bottom: 5px; color: var(--text-color);">智能标签</div>
                <div style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">推荐相关标签</div>
                <button onclick="suggestTags()" style="padding: 8px 16px; background: var(--button-primary); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">推荐标签</button>
            </div>

            <div style="background: rgba(139, 92, 246, 0.1); padding: 15px; border-radius: 12px; border: 1px solid rgba(139, 92, 246, 0.2);">
                <div style="font-size: 24px; margin-bottom: 8px;">📝</div>
                <div style="font-weight: 600; margin-bottom: 5px; color: var(--text-color);">内容摘要</div>
                <div style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">生成内容摘要</div>
                <button onclick="generateSummaries()" style="padding: 8px 16px; background: var(--button-secondary); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">生成摘要</button>
            </div>
        </div>

        <div id="aiResults" style="margin-top: 20px; padding: 15px; background: rgba(0, 0, 0, 0.05); border-radius: 8px; display: none;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">AI分析结果</h4>
            <div id="aiResultsContent"></div>
        </div>

        <button onclick="hideAIPanel()" style="padding: 8px 16px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
    </div>

    <!-- 📈 数据可视化面板 -->
    <div id="dataVisualizationPanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 95%; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600; text-align: center;">📈 数据可视化分析</h3>

        <!-- 统计概览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 25px;">
            <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 15px; border-radius: 12px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold;" id="totalCount">0</div>
                <div style="font-size: 12px; opacity: 0.9;">总内容数</div>
            </div>
            <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 15px; border-radius: 12px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold;" id="sectionCount">0</div>
                <div style="font-size: 12px; opacity: 0.9;">分区数量</div>
            </div>
            <div style="background: linear-gradient(135deg, #0ea5e9, #0284c7); color: white; padding: 15px; border-radius: 12px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold;" id="tagCount">0</div>
                <div style="font-size: 12px; opacity: 0.9;">标签数量</div>
            </div>
            <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; padding: 15px; border-radius: 12px; text-align: center;">
                <div style="font-size: 24px; font-weight: bold;" id="avgLength">0</div>
                <div style="font-size: 12px; opacity: 0.9;">平均长度</div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
            <!-- 分区分布图 -->
            <div style="background: rgba(255, 255, 255, 0.5); padding: 15px; border-radius: 12px; border: 1px solid rgba(0, 0, 0, 0.1);">
                <h4 style="margin: 0 0 15px 0; color: var(--text-color); text-align: center;">📊 分区分布</h4>
                <div id="sectionChart" style="height: 200px; display: flex; align-items: end; justify-content: space-around; gap: 5px;"></div>
            </div>

            <!-- 时间分布图 -->
            <div style="background: rgba(255, 255, 255, 0.5); padding: 15px; border-radius: 12px; border: 1px solid rgba(0, 0, 0, 0.1);">
                <h4 style="margin: 0 0 15px 0; color: var(--text-color); text-align: center;">📅 时间分布</h4>
                <div id="timeChart" style="height: 200px; display: flex; align-items: end; justify-content: space-around; gap: 2px;"></div>
            </div>
        </div>

        <!-- 标签云 -->
        <div style="background: rgba(255, 255, 255, 0.5); padding: 15px; border-radius: 12px; border: 1px solid rgba(0, 0, 0, 0.1); margin-bottom: 20px;">
            <h4 style="margin: 0 0 15px 0; color: var(--text-color); text-align: center;">🏷️ 标签云</h4>
            <div id="tagCloud" style="text-align: center; line-height: 2;"></div>
        </div>

        <!-- 活跃度热力图 -->
        <div style="background: rgba(255, 255, 255, 0.5); padding: 15px; border-radius: 12px; border: 1px solid rgba(0, 0, 0, 0.1); margin-bottom: 20px;">
            <h4 style="margin: 0 0 15px 0; color: var(--text-color); text-align: center;">🔥 活跃度热力图 (最近7天)</h4>
            <div id="heatMap" style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 3px; max-width: 300px; margin: 0 auto;"></div>
        </div>

        <div style="text-align: center;">
            <button onclick="refreshDataVisualization()" style="padding: 10px 20px; background: var(--button-primary); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); margin-right: 10px;">🔄 刷新数据</button>
            <button onclick="hideDataVisualization()" style="padding: 10px 20px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
        </div>
    </div>



    <!-- 简洁数据恢复面板 -->
    <div id="dataRecoveryPanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 90%; text-align: center; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600;">🔧 数据恢复工具</h3>
        <p style="color: var(--text-medium); margin-bottom: 15px; line-height: 1.5;">如果您的数据丢失或显示异常，请尝试以下恢复选项：</p>
        <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; margin-bottom: 15px;">
            <button onclick="checkLocalStorageData()" style="padding: 10px 16px; background: var(--button-info); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); box-shadow: var(--shadow);">检查本地数据</button>
            <button onclick="restoreFromBackup()" style="padding: 10px 16px; background: var(--button-success); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); box-shadow: var(--shadow);">从备份恢复</button>
            <button onclick="showRawData()" style="padding: 10px 16px; background: var(--button-warning); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); box-shadow: var(--shadow);">查看原始数据</button>
            <button onclick="clearAndReset()" style="padding: 10px 16px; background: var(--button-danger); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); box-shadow: var(--shadow);">清空重置</button>
        </div>
        <button onclick="hideDataRecovery()" style="padding: 8px 16px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
    </div>



    <!-- 🛠️ 简洁功能面板 -->
    <div style="background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); margin: 20px auto; width: 96%; box-shadow: var(--shadow); backdrop-filter: var(--backdrop-blur); overflow: hidden;">
        <div style="background: var(--bg-secondary); padding: 20px; cursor: pointer; transition: var(--transition);" onclick="toggleCollapsibleSection('toolbox')">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <h3 style="color: var(--text-light); font-size: 1.3rem; margin: 0; display: flex; align-items: center; gap: 10px; font-weight: 600;">
                    <span style="font-size: 1.4em;">🛠️</span>
                    功能面板
                </h3>
                <button style="background: rgba(255,255,255,0.2); border: none; color: var(--text-light); font-size: 1.2em; cursor: pointer; transition: var(--transition); border-radius: 50%; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;" id="toolboxToggle">🔼</button>
            </div>
        </div>
        <div class="collapsible-content" id="toolboxContent">
            <!-- 分区管理 -->
            <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid var(--card-border);">
                <h4 style="margin: 0 0 10px 0; display: flex; align-items: center; gap: 6px; font-size: 14px;">📂 分区管理</h4>
                <div class="section-tabs" id="sectionTabs">
                    <div class="section-tab active" data-section="all" onclick="switchSection('all')">
                        <span>全部</span>
                        <span class="section-count" id="allCount">0</span>
                    </div>
                </div>
                
                <div class="add-section-container">
                    <input type="text" class="add-section-input" id="newSectionName" placeholder="输入新分区名称..." onkeydown="handleSectionInput(event)">
                    <button class="add-section-btn" onclick="addNewSection()">➕ 添加</button>
                </div>
                
                <!-- 批量管理 -->
                <div class="batch-management">
                    <div class="batch-controls">
                        <button onclick="toggleBatchMode()" id="batchModeBtn">开启批量</button>
                        <div id="batchActions" style="display: none; gap: 8px; align-items: center;">
                            <select id="batchTargetSection" class="add-section-input" style="width: auto; padding: 5px;">
                                <option value="">选择目标分区</option>
                            </select>
                            <button onclick="batchMoveToSection()" style="background: var(--button-success); color: white;">移动</button>
                            <button onclick="batchDelete()" style="background: var(--button-danger); color: white;">删除</button>
                        </div>
                        <span id="batchCounter">已选择 0 项</span>
                    </div>
                </div>
            </div>
            
            <!-- 搜索功能 -->
            <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid var(--card-border);">
                <h4 style="margin: 0 0 10px 0; display: flex; align-items: center; gap: 6px; font-size: 14px;">🔍 搜索功能</h4>
                <div class="search-container" style="margin: 0; width: 100%;">
                    <input type="text" id="searchInput" placeholder="🔍 搜索内容、标签、分区..."">
                    <button onclick="performSearchFromButton()" style="background: var(--button-primary); color: white;">🔍 搜索</button>
                    <button onclick="clearSearch()">清除</button>
                </div>
            </div>
            
            <!-- 排序和控制 -->
            <div>
                <h4 style="margin: 0 0 10px 0; display: flex; align-items: center; gap: 6px; font-size: 14px;">⚙️ 排序和控制</h4>
                <div class="sort-options" style="margin: 0 0 15px 0; width: 100%;">
                    <button onclick="sortSnippets('original')">原始顺序</button>
                    <button onclick="sortSnippets('date-asc')">按时间升序</button>
                    <button onclick="sortSnippets('date-desc')">按时间降序</button>
                    <button onclick="sortSnippets('text-asc')">按文本升序</button>
                    <button onclick="sortSnippets('text-desc')">按文本降序</button>
                </div>
                
                <div class="expand-control-container" style="margin: 0 0 10px 0;">
                    <button id="toggleAllExpandBtn" onclick="toggleAllExpand()" class="toggle-all-expand-btn">
                        <span id="toggleAllIcon">📤</span> 全部折叠
                    </button>
                </div>

                <!-- 快速操作区域 -->
                <div class="quick-actions" style="margin: 10px 0; padding: 10px; background: rgba(37, 99, 235, 0.05); border-radius: 8px; border: 1px solid rgba(37, 99, 235, 0.1);">
                    <h4 style="margin: 0 0 8px 0; font-size: 14px; display: flex; align-items: center; gap: 6px;">⚡ 快速操作</h4>
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <button onclick="quickCopyAll()" style="padding: 6px 12px; background: var(--button-info); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">📋 复制全部</button>
                        <button onclick="quickCopyVisible()" style="padding: 6px 12px; background: var(--button-primary); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">👁️ 复制可见</button>
                        <button onclick="showQuickTagManager()" style="padding: 6px 12px; background: var(--button-success); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">🏷️ 标签管理</button>
                        <button onclick="showSearchSuggestions()" style="padding: 6px 12px; background: var(--button-warning); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">🔍 搜索建议</button>
                    </div>
                </div>

            </div>

            <!-- 显示控制 -->
            <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid var(--card-border);">
                <h4 style="margin: 0 0 8px 0; font-size: 14px; display: flex; align-items: center; gap: 6px;">👁️ 显示控制</h4>
                <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                    <label style="display: flex; align-items: center; gap: 5px; font-size: 12px; cursor: pointer;">
                        <input type="checkbox" id="showSectionInfoCheck" onchange="toggleSectionInfo()" style="cursor: pointer;">
                        显示分区信息
                    </label>
                    <label style="display: flex; align-items: center; gap: 5px; font-size: 12px; cursor: pointer;">
                        <input type="checkbox" id="showQuickSectionsCheck" onchange="toggleQuickSections()" style="cursor: pointer;">
                        显示快速分区
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- 🏷️ 快速标签管理面板 -->
    <div id="quickTagPanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 90%; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600; text-align: center;">🏷️ 标签管理中心</h3>

        <!-- 标签统计 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; margin-bottom: 20px;">
            <div style="background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark)); color: white; padding: 12px; border-radius: 8px; text-align: center;">
                <div style="font-size: 18px; font-weight: bold;" id="totalTagsCount">0</div>
                <div style="font-size: 11px; opacity: 0.9;">总标签数</div>
            </div>
            <div style="background: linear-gradient(135deg, var(--success), #059669); color: white; padding: 12px; border-radius: 8px; text-align: center;">
                <div style="font-size: 18px; font-weight: bold;" id="usedTagsCount">0</div>
                <div style="font-size: 11px; opacity: 0.9;">已使用</div>
            </div>
            <div style="background: linear-gradient(135deg, var(--warning), #d97706); color: white; padding: 12px; border-radius: 8px; text-align: center;">
                <div style="font-size: 18px; font-weight: bold;" id="unusedTagsCount">0</div>
                <div style="font-size: 11px; opacity: 0.9;">未使用</div>
            </div>
        </div>

        <!-- 标签列表 -->
        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">所有标签</h4>
            <div id="allTagsList" style="display: flex; flex-wrap: wrap; gap: 6px; max-height: 200px; overflow-y: auto; padding: 10px; background: rgba(0,0,0,0.02); border-radius: 8px;"></div>
        </div>

        <!-- 批量操作 -->
        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">批量操作</h4>
            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                <button onclick="cleanUnusedTags()" style="padding: 8px 12px; background: var(--button-danger); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">🗑️ 清理未使用</button>
                <button onclick="mergeSelectedTags()" style="padding: 8px 12px; background: var(--button-warning); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">🔗 合并标签</button>
                <button onclick="exportTags()" style="padding: 8px 12px; background: var(--button-info); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">📤 导出标签</button>
            </div>
        </div>

        <div style="text-align: center;">
            <button onclick="hideQuickTagManager()" style="padding: 8px 16px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
        </div>
    </div>

    <!-- 🔍 搜索建议面板 -->
    <div id="searchSuggestionsPanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 90%; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600; text-align: center;">🔍 智能搜索建议</h3>

        <!-- 热门搜索 -->
        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">🔥 热门搜索</h4>
            <div id="popularSearches" style="display: flex; flex-wrap: wrap; gap: 6px;"></div>
        </div>

        <!-- 最近搜索 -->
        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">🕒 最近搜索</h4>
            <div id="recentSearches" style="display: flex; flex-wrap: wrap; gap: 6px;"></div>
        </div>

        <!-- 推荐搜索 -->
        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">💡 推荐搜索</h4>
            <div id="suggestedSearches" style="display: flex; flex-wrap: wrap; gap: 6px;"></div>
        </div>

        <div style="text-align: center;">
            <button onclick="clearSearchHistory()" style="padding: 8px 16px; background: var(--button-danger); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition); margin-right: 10px;">清空历史</button>
            <button onclick="hideSearchSuggestions()" style="padding: 8px 16px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
        </div>
    </div>

    <!-- 添加内容 -->
    <div class="collapsible-section">
        <div class="collapsible-header" onclick="toggleCollapsibleSection('addContent')">
            <h3>➕ 添加内容</h3>
            <button class="collapsible-toggle" id="addContentToggle">🔽</button>
        </div>
        <div class="collapsible-content" id="addContentContent">
            <div class="add-area" style="margin: 0; width: 100%;">
                <button class="advanced-toggle-btn" onclick="toggleAdvancedFeatures('top')">⚙️ 高级功能</button>
                <textarea id="newSnippetTop" placeholder="输入新的剪贴板内容"></textarea>
                
                <div class="advanced-features" id="advanced-features-top">
                    <div class="upload-container">
                        <input type="file" id="fileUploadTop" style="display: none;">
                        <button onclick="document.getElementById('fileUploadTop').click()">📄 选择文件</button>
                        <span id="fileNameTop"></span>
                        <input type="file" id="imageUploadTop" accept="image/*" style="display: none;">
                        <button onclick="document.getElementById('imageUploadTop').click()">🖼️ 选择图片</button>
                    </div>
                    <img id="imagePreviewTop" alt="图片预览" style="display:none;">
                    
                    <div class="tags-input-container">
                        <div id="tags-display-top" class="tags-display"></div>
                        <input type="text" class="tag-input" placeholder="添加标签 (回车确认)" onkeydown="handleTagInput(event, 'top')">
                    </div>
                    
                    <!-- 分区选择 -->
                    <div class="section-selector-container">
                        <label>选择分区：</label>
                        <select id="sectionSelect-top">
                            <option value="">默认</option>
                        </select>
                    </div>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="add-button" style="flex: 1;" onclick="addSnippet('top')">添加</button>
                    <button id="recordButton" class="media-button" onclick="toggleRecording()" style="padding: 12px; background: var(--button-warning); color: white; border: none; border-radius: var(--button-radius); font-weight: 600; cursor: pointer; transition: var(--transition); box-shadow: var(--shadow);">🎤</button>
                </div>
            </div>
        </div>
    </div>

    <div id="snippets"></div>

    <div class="export-import-container" style="background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 20px auto; width: 90%; text-align: center; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.1rem; font-weight: 600; display: flex; align-items: center; justify-content: center; gap: 8px;">
            <span style="font-size: 1.3em;">📁</span>
            数据管理
        </h3>

        <!-- 存储统计 -->
        <div id="storageStats" style="margin-bottom: 15px; padding: 10px; background: rgba(37, 99, 235, 0.05); border-radius: 8px; border: 1px solid rgba(37, 99, 235, 0.1);">
            <div style="font-size: 12px; color: var(--text-medium); margin-bottom: 5px;">存储使用情况</div>
            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 11px;">
                <span id="storageUsed">已使用: 0 KB</span>
                <span id="storageCompression">压缩率: 0%</span>
                <span id="lastBackup">最后备份: 无</span>
            </div>
            <div style="width: 100%; height: 4px; background: var(--gray-200); border-radius: 2px; margin-top: 5px; overflow: hidden;">
                <div id="storageBar" style="height: 100%; background: var(--primary-blue); width: 0%; transition: width 0.3s ease;"></div>
            </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center; flex-wrap: wrap;">
            <button onclick="exportData()" class="export-button" style="padding: 12px 20px; background: var(--button-success); color: white; border: none; border-radius: var(--button-radius); font-weight: 600; font-size: 14px; transition: var(--transition); box-shadow: var(--shadow);">
                📤 导出数据
            </button>
            <button onclick="document.getElementById('importFile').click()" class="import-button" style="padding: 12px 20px; background: var(--button-info); color: white; border: none; border-radius: var(--button-radius); font-weight: 600; font-size: 14px; transition: var(--transition); box-shadow: var(--shadow);">
                📥 导入数据
            </button>
            <button onclick="showStorageManager()" style="padding: 12px 20px; background: var(--button-warning); color: white; border: none; border-radius: var(--button-radius); font-weight: 600; font-size: 14px; transition: var(--transition); box-shadow: var(--shadow);">
                🗄️ 存储管理
            </button>
            <button onclick="showExportOptions()" style="padding: 12px 20px; background: var(--button-secondary); color: white; border: none; border-radius: var(--button-radius); font-weight: 600; font-size: 14px; transition: var(--transition); box-shadow: var(--shadow);">
                📋 多格式导出
            </button>
        </div>
        <input type="file" id="importFile" style="display: none;" accept=".json" onchange="importData(event)">
    </div>

    <!-- 🗄️ 存储管理面板 -->
    <div id="storageManagerPanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 90%; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600; text-align: center;">🗄️ 存储管理中心</h3>

        <!-- 详细存储统计 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 20px;">
            <div style="background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark)); color: white; padding: 12px; border-radius: 8px; text-align: center;">
                <div style="font-size: 18px; font-weight: bold;" id="totalStorageUsed">0 KB</div>
                <div style="font-size: 11px; opacity: 0.9;">总使用量</div>
            </div>
            <div style="background: linear-gradient(135deg, var(--success), #059669); color: white; padding: 12px; border-radius: 8px; text-align: center;">
                <div style="font-size: 18px; font-weight: bold;" id="compressionRatio">0%</div>
                <div style="font-size: 11px; opacity: 0.9;">压缩率</div>
            </div>
            <div style="background: linear-gradient(135deg, var(--warning), #d97706); color: white; padding: 12px; border-radius: 8px; text-align: center;">
                <div style="font-size: 18px; font-weight: bold;" id="backupCount">0</div>
                <div style="font-size: 11px; opacity: 0.9;">备份数量</div>
            </div>
            <div style="background: linear-gradient(135deg, var(--info), #0284c7); color: white; padding: 12px; border-radius: 8px; text-align: center;">
                <div style="font-size: 18px; font-weight: bold;" id="dataItems">0</div>
                <div style="font-size: 11px; opacity: 0.9;">数据条目</div>
            </div>
        </div>

        <!-- 存储项目列表 -->
        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">存储项目</h4>
            <div id="storageItemsList" style="max-height: 200px; overflow-y: auto; border: 1px solid var(--card-border); border-radius: 8px; padding: 10px; background: rgba(0,0,0,0.02);"></div>
        </div>

        <!-- 管理操作 -->
        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">管理操作</h4>
            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                <button onclick="optimizeStorage()" style="padding: 8px 12px; background: var(--button-primary); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">🗜️ 优化存储</button>
                <button onclick="createManualBackup()" style="padding: 8px 12px; background: var(--button-success); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">💾 手动备份</button>
                <button onclick="cleanOldBackups()" style="padding: 8px 12px; background: var(--button-warning); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">🧹 清理备份</button>
                <button onclick="resetStorage()" style="padding: 8px 12px; background: var(--button-danger); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">🔄 重置存储</button>
            </div>
        </div>

        <!-- 自动备份设置 -->
        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">自动备份设置</h4>
            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                    <input type="checkbox" id="autoBackupEnabled" onchange="toggleAutoBackup()" style="cursor: pointer;">
                    启用自动备份
                </label>
                <select id="backupInterval" style="padding: 4px 8px; border: 1px solid var(--card-border); border-radius: 4px; font-size: 12px;">
                    <option value="5">每5分钟</option>
                    <option value="15" selected>每15分钟</option>
                    <option value="30">每30分钟</option>
                    <option value="60">每小时</option>
                </select>
            </div>
        </div>

        <div style="text-align: center;">
            <button onclick="hideStorageManager()" style="padding: 8px 16px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
        </div>
    </div>

    <!-- 📋 多格式导出面板 -->
    <div id="exportOptionsPanel" style="display: none; background: var(--card-bg); border: var(--glass-border); border-radius: var(--border-radius); padding: 20px; margin: 15px auto; width: 90%; backdrop-filter: var(--backdrop-blur); box-shadow: var(--shadow);">
        <h3 style="color: var(--text-color); margin-bottom: 15px; font-size: 1.2rem; font-weight: 600; text-align: center;">📋 多格式导出</h3>

        <!-- 导出选项 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
            <div style="background: rgba(37, 99, 235, 0.05); padding: 15px; border-radius: 8px; border: 1px solid rgba(37, 99, 235, 0.1);">
                <h4 style="margin: 0 0 10px 0; color: var(--primary-blue); font-size: 14px;">📝 Markdown</h4>
                <p style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">导出为Markdown格式，支持标题、列表和代码块</p>
                <button onclick="exportAsMarkdown()" style="width: 100%; padding: 8px; background: var(--primary-blue); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">导出 Markdown</button>
            </div>

            <div style="background: rgba(16, 185, 129, 0.05); padding: 15px; border-radius: 8px; border: 1px solid rgba(16, 185, 129, 0.1);">
                <h4 style="margin: 0 0 10px 0; color: var(--success); font-size: 14px;">📊 CSV</h4>
                <p style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">导出为CSV表格格式，便于在Excel中打开</p>
                <button onclick="exportAsCSV()" style="width: 100%; padding: 8px; background: var(--success); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">导出 CSV</button>
            </div>

            <div style="background: rgba(245, 158, 11, 0.05); padding: 15px; border-radius: 8px; border: 1px solid rgba(245, 158, 11, 0.1);">
                <h4 style="margin: 0 0 10px 0; color: var(--warning); font-size: 14px;">📄 TXT</h4>
                <p style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">导出为纯文本格式，兼容性最好</p>
                <button onclick="exportAsTXT()" style="width: 100%; padding: 8px; background: var(--warning); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">导出 TXT</button>
            </div>

            <div style="background: rgba(239, 68, 68, 0.05); padding: 15px; border-radius: 8px; border: 1px solid rgba(239, 68, 68, 0.1);">
                <h4 style="margin: 0 0 10px 0; color: var(--danger); font-size: 14px;">📑 PDF</h4>
                <p style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">导出为PDF文档，保持格式美观</p>
                <button onclick="exportAsPDF()" style="width: 100%; padding: 8px; background: var(--danger); color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">导出 PDF</button>
            </div>

            <div style="background: rgba(139, 92, 246, 0.05); padding: 15px; border-radius: 8px; border: 1px solid rgba(139, 92, 246, 0.1);">
                <h4 style="margin: 0 0 10px 0; color: #8b5cf6; font-size: 14px;">🌐 HTML</h4>
                <p style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">导出为HTML网页，可在浏览器中查看</p>
                <button onclick="exportAsHTML()" style="width: 100%; padding: 8px; background: #8b5cf6; color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">导出 HTML</button>
            </div>

            <div style="background: rgba(6, 182, 212, 0.05); padding: 15px; border-radius: 8px; border: 1px solid rgba(6, 182, 212, 0.1);">
                <h4 style="margin: 0 0 10px 0; color: #06b6d4; font-size: 14px;">⚙️ JSON</h4>
                <p style="font-size: 12px; color: var(--text-medium); margin-bottom: 10px;">导出为JSON格式，便于程序处理</p>
                <button onclick="exportAsJSON()" style="width: 100%; padding: 8px; background: #06b6d4; color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">导出 JSON</button>
            </div>
        </div>

        <!-- 导出设置 -->
        <div style="margin-bottom: 20px; padding: 15px; background: rgba(0,0,0,0.02); border-radius: 8px;">
            <h4 style="margin: 0 0 10px 0; color: var(--text-color);">导出设置</h4>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: center;">
                <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                    <input type="checkbox" id="includeTimestamp" checked style="cursor: pointer;">
                    包含时间戳
                </label>
                <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                    <input type="checkbox" id="includeTags" checked style="cursor: pointer;">
                    包含标签
                </label>
                <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                    <input type="checkbox" id="includeSection" checked style="cursor: pointer;">
                    包含分区
                </label>
                <label style="display: flex; align-items: center; gap: 5px; font-size: 12px;">
                    <input type="checkbox" id="onlyVisible" style="cursor: pointer;">
                    仅导出可见内容
                </label>
            </div>
        </div>

        <div style="text-align: center;">
            <button onclick="hideExportOptions()" style="padding: 8px 16px; background: var(--text-dim); color: white; border: none; border-radius: var(--button-radius); font-weight: 500; transition: var(--transition);">关闭</button>
        </div>
    </div>

    <!-- ⌨️ 键盘快捷键提示 -->
    <div id="keyboardHint" class="keyboard-hint">
        <div style="font-weight: bold; margin-bottom: 8px; color: var(--highlight-color);">⌨️ 键盘快捷键</div>
        <div><kbd>Ctrl</kbd> + <kbd>Enter</kbd> 快速添加</div>
        <div><kbd>Ctrl</kbd> + <kbd>F</kbd> 搜索内容</div>
        <div><kbd>Ctrl</kbd> + <kbd>D</kbd> 切换深色模式</div>
        <div><kbd>Ctrl</kbd> + <kbd>Z</kbd> 撤销操作</div>
        <div><kbd>Ctrl</kbd> + <kbd>Y</kbd> 重做操作</div>
        <div><kbd>Ctrl</kbd> + <kbd>A</kbd> 全选 (批量模式)</div>
        <div><kbd>Ctrl</kbd> + <kbd>B</kbd> 切换批量模式</div>
        <div><kbd>Ctrl</kbd> + <kbd>S</kbd> 导出数据</div>
        <div><kbd>Delete</kbd> 删除选中项</div>
        <div><kbd>Escape</kbd> 退出/清除</div>
        <div><kbd>F1</kbd> 显示/隐藏此提示</div>
    </div>

    <script>
        // 全局变量
        let currentImageBase64 = '';
        let currentFileData = null;
        let editingIndex = null;
        let currentSort = 'original';
        let currentSearchTerm = '';
        let currentTags = [];
        let allTags = new Set();
        let currentTagFilter = 'all';
        let isAllExpanded = true;
        let currentSection = 'all';
        let allSections = new Set(['默认']);

        // 批量管理相关变量
        let batchMode = false;
        let selectedIndices = new Set();

        // 显示控制变量
        let showSectionInfo = false;  // 默认隐藏分区信息
        let showQuickSections = false;  // 默认隐藏快速分区

        // 拖拽相关变量
        let draggedElement = null;
        let draggedIndex = -1;
        let dropIndicator = null;

        // 撤销重做相关变量
        let undoStack = [];
        let redoStack = [];
        const MAX_UNDO_STEPS = 20;

        // 获取DOM元素
        const snippetsDiv = document.getElementById('snippets');
        const newSnippetTextareaTop = document.getElementById('newSnippetTop');
        const imageUploadTop = document.getElementById('imageUploadTop');
        const fileUploadTop = document.getElementById('fileUploadTop');
        const imagePreviewTop = document.getElementById('imagePreviewTop');
        const fileNameTop = document.getElementById('fileNameTop');

        // 初始化
        window.addEventListener('DOMContentLoaded', function() {
            // 先加载分区信息
            loadSections();
            
            // 加载保存的排序状态
            const savedSort = localStorage.getItem('currentSort');
            if (savedSort) {
                currentSort = savedSort;
            }
            
            loadSnippets();
            updateSectionTabs();
            updateSectionSelects();
            
            // 初始化显示设置
            initDisplaySettings();

            // 延迟初始化折叠状态，确保DOM完全加载
            setTimeout(() => {
                initCollapsibleSections();
            }, 300);
            
            // 设置事件监听器
            setupInputHandlers('top');

            // 🔧 初始化按钮文本
            updateAddButtonText();

            // 深色模式
            const savedDarkMode = localStorage.getItem('darkMode');
            if (savedDarkMode === 'enabled') {
                document.body.classList.add('dark-mode');
                document.getElementById('darkModeIcon').textContent = '☀️';
            }

            // 初始化键盘快捷键
            initKeyboardShortcuts();

            // 显示键盘快捷键提示（3秒后自动隐藏）
            setTimeout(() => {
                showKeyboardHint();
                setTimeout(hideKeyboardHint, 3000);
            }, 1000);

            // 确保按钮文本正确初始化
            setTimeout(() => {
                editingIndex = null; // 强制重置编辑状态
                updateAddButtonText();
            }, 100);
        });

        // 加载保存的分区信息
        function loadSections() {
            try {
                const savedSections = localStorage.getItem('sections');
                if (savedSections) {
                    allSections = new Set(JSON.parse(savedSections));
                } else {
                    allSections = new Set(['默认']);
                }
            } catch (error) {
                console.error('加载分区信息失败:', error);
                allSections = new Set(['默认']);
            }
        }

        // 保存分区信息
        function saveSections() {
            try {
                localStorage.setItem('sections', JSON.stringify([...allSections]));
                return true;
            } catch (error) {
                console.error('保存分区信息失败:', error);
                return false;
            }
        }

        // 设置输入处理器
        function setupInputHandlers(position) {
            const textarea = newSnippetTextareaTop;
            const imageUpload = imageUploadTop;
            const fileUpload = fileUploadTop;

            textarea.addEventListener('keydown', function(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    addSnippet(position);
                }
            });

            imageUpload.addEventListener('change', function(event) {
                handleImageUpload(position, event);
            });

            fileUpload.addEventListener('change', function(event) {
                handleFileUpload(position, event);
            });
        }

        // 处理图片上传
        function handleImageUpload(position, event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 5 * 1024 * 1024) {
                    alert('图片大小不能超过5MB');
                    return;
                }
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreviewTop.src = e.target.result;
                    imagePreviewTop.style.display = "block";
                    currentImageBase64 = e.target.result;
                }
                reader.readAsDataURL(file);
            }
        }

        // 处理文件上传
        function handleFileUpload(position, event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 10 * 1024 * 1024) {
                    alert('文件大小不能超过10MB');
                    return;
                }
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentFileData = {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        data: e.target.result
                    };
                    fileNameTop.textContent = file.name;
                }
                reader.readAsDataURL(file);
            }
        }

        // 获取snippets数据（支持压缩格式）
        function getSnippets() {
            try {
                const rawData = localStorage.getItem('snippets') || '[]';
                const isCompressed = localStorage.getItem('snippets_compressed') === 'true';

                let snippets;
                if (isCompressed) {
                    // 尝试解压缩数据
                    snippets = decompressData(rawData);
                    if (!snippets) {
                        // 如果解压缩失败，尝试直接解析
                        snippets = JSON.parse(rawData);
                    }
                } else {
                    snippets = JSON.parse(rawData);
                }
                
                // 完全保持原有数据格式，只在必要时添加缺失字段
                return snippets.map((snippet, originalIndex) => {
                    if (typeof snippet === 'string') {
                        // 如果是旧格式的字符串，转换为对象但保留原有内容和顺序
                        return { 
                            content: snippet, 
                            timestamp: Date.now() - (snippets.length - originalIndex) * 60000, // 使用分钟间隔保持顺序
                            index: originalIndex,
                            originalIndex: originalIndex, // 保存原始位置
                            pinned: false,
                            section: '默认',
                            tags: []
                        };
                    } else {
                        // 如果已经是对象，完全保留原有数据，只补充缺失的字段
                        return {
                            content: snippet.content || '',
                            // 优先使用原有时间戳，如果没有则根据原始位置生成
                            timestamp: snippet.timestamp || (Date.now() - (snippets.length - originalIndex) * 60000),
                            index: snippet.index !== undefined ? snippet.index : originalIndex,
                            originalIndex: snippet.originalIndex !== undefined ? snippet.originalIndex : originalIndex,
                            pinned: snippet.pinned || false,
                            section: snippet.section || '默认',
                            tags: snippet.tags || [],
                            // 保留所有其他可能存在的字段
                            ...snippet
                        };
                    }
                });
            } catch (error) {
                console.error('读取数据出错:', error);
                // 尝试从备份恢复
                try {
                    const backup = localStorage.getItem('snippets_backup_auto');
                    if (backup) {
                        console.log('尝试从自动备份恢复数据');
                        const backupData = JSON.parse(backup);
                        return backupData.map((snippet, originalIndex) => {
                            if (typeof snippet === 'string') {
                                return { 
                                    content: snippet, 
                                    timestamp: Date.now() - (backupData.length - originalIndex) * 60000,
                                    index: originalIndex,
                                    originalIndex: originalIndex,
                                    pinned: false,
                                    section: '默认',
                                    tags: []
                                };
                            }
                            return { ...snippet, originalIndex: originalIndex };
                        });
                    }
                } catch (e) {
                    console.error('备份恢复也失败:', e);
                }
                return [];
            }
        }

        // 🗜️ 数据压缩和存储优化

        // 简单的数据压缩函数
        function compressData(data) {
            try {
                const jsonString = JSON.stringify(data);
                // 使用简单的压缩算法：移除重复的空格和换行
                const compressed = jsonString.replace(/\s+/g, ' ').trim();
                return compressed;
            } catch (error) {
                console.error('压缩失败:', error);
                return JSON.stringify(data);
            }
        }

        // 解压缩数据
        function decompressData(compressedData) {
            try {
                return JSON.parse(compressedData);
            } catch (error) {
                console.error('解压缩失败:', error);
                return null;
            }
        }

        // 创建自动备份
        function createAutoBackup() {
            try {
                const currentData = localStorage.getItem('snippets');
                if (currentData) {
                    const timestamp = new Date().toISOString();
                    localStorage.setItem('snippets_backup_auto', currentData);
                    localStorage.setItem('backup_timestamp', timestamp);

                    // 保持最近5个备份
                    const backupKeys = Object.keys(localStorage).filter(key => key.startsWith('snippets_backup_'));
                    if (backupKeys.length > 5) {
                        backupKeys.sort().slice(0, -5).forEach(key => {
                            localStorage.removeItem(key);
                        });
                    }
                }
            } catch (error) {
                console.error('创建备份失败:', error);
            }
        }

        // 更新存储统计
        function updateStorageStats() {
            try {
                const used = JSON.stringify(localStorage).length;
                const quota = 5 * 1024 * 1024; // 假设5MB配额
                const percentage = (used / quota * 100).toFixed(1);

                localStorage.setItem('storage_stats', JSON.stringify({
                    used: used,
                    quota: quota,
                    percentage: percentage,
                    lastUpdate: new Date().toISOString()
                }));
            } catch (error) {
                console.error('更新存储统计失败:', error);
            }
        }

        // 优化的保存函数
        function saveSnippets(snippets) {
            try {
                // 创建备份
                createAutoBackup();

                // 压缩数据
                const compressedData = compressData(snippets);

                // 保存压缩后的数据
                localStorage.setItem('snippets', compressedData);
                localStorage.setItem('snippets_compressed', 'true');
                localStorage.setItem('last_save_time', new Date().toISOString());

                // 更新存储统计
                updateStorageStats();

                return true;
            } catch (error) {
                console.error('保存失败:', error);

                // 如果压缩保存失败，尝试不压缩保存
                try {
                    localStorage.setItem('snippets', JSON.stringify(snippets));
                    localStorage.setItem('snippets_compressed', 'false');
                    showWarningMessage('数据已保存，但未压缩（存储空间可能不足）');
                    return true;
                } catch (fallbackError) {
                    showErrorMessage('保存失败，可能是存储空间不足');
                    return false;
                }
            }
        }

        // 加载snippets
        function loadSnippets() {
            const snippets = getSnippets();

            // 提取所有标签和分区
            allTags = new Set();
            const foundSections = new Set(['默认']);
            snippets.forEach(snippet => {
                if (snippet.tags && Array.isArray(snippet.tags)) {
                    snippet.tags.forEach(tag => allTags.add(tag));
                }
                if (snippet.section) {
                    foundSections.add(snippet.section);
                }
            });
            
            // 合并已有分区和从数据中发现的分区
            foundSections.forEach(section => allSections.add(section));
            
            // 保存分区信息
            saveSections();

            // 过滤分区
            let filteredSnippets = snippets;
            if (currentSection !== 'all') {
                filteredSnippets = snippets.filter(snippet => 
                    (snippet.section || '默认') === currentSection
                );
            }

            // 增强搜索过滤 - 支持内容、标签、分区搜索
            if (currentSearchTerm) {
                filteredSnippets = filteredSnippets.filter(snippet =>
                    matchesSearch(snippet, currentSearchTerm)
                );
            }

            // 排序
            const sortedSnippets = sortSnippetsData(filteredSnippets, currentSort);

            // 显示
            displaySnippets(sortedSnippets);
            updateSectionTabs();
            updateSectionSelects();
        }

        // 优化的显示snippets函数 - 支持虚拟滚动
        let visibleSnippets = [];
        let allSnippetsData = [];
        const ITEMS_PER_PAGE = 50; // 每页显示的项目数
        let currentPage = 0;

        function displaySnippets(snippets) {
            allSnippetsData = snippets;

            if (snippets.length === 0) {
                snippetsDiv.innerHTML = '<p style="text-align: center; color: var(--text-dim); padding: 40px;">暂无数据</p>';
                return;
            }

            // 如果数据量大，使用分页显示
            if (snippets.length > ITEMS_PER_PAGE) {
                displaySnippetsWithPagination(snippets);
            } else {
                displayAllSnippets(snippets);
            }
        }

        function displayAllSnippets(snippets) {
            // 使用文档片段优化DOM操作
            const fragment = document.createDocumentFragment();

            snippets.forEach(snippetData => {
                const snippetDiv = createSnippetElement(snippetData);
                fragment.appendChild(snippetDiv);
            });

            // 一次性更新DOM
            snippetsDiv.innerHTML = '';
            snippetsDiv.appendChild(fragment);
        }

        function displaySnippetsWithPagination(snippets) {
            const startIndex = currentPage * ITEMS_PER_PAGE;
            const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, snippets.length);
            const pageSnippets = snippets.slice(startIndex, endIndex);

            displayAllSnippets(pageSnippets);

            // 添加分页控制
            addPaginationControls(snippets.length);

            // 添加无限滚动
            addInfiniteScroll(snippets);
        }

        function createSnippetElement(snippetData) {
            const snippetDiv = document.createElement('div');
            snippetDiv.classList.add('snippet');
            snippetDiv.dataset.index = snippetData.index;

            // 添加拖拽属性
            snippetDiv.draggable = true;

            if (snippetData.pinned) {
                snippetDiv.classList.add('pinned');
            }

            // 批量模式下的选择效果
            if (batchMode && selectedIndices.has(snippetData.index)) {
                snippetDiv.style.background = 'rgba(37, 99, 235, 0.1)';
                snippetDiv.style.border = '2px solid var(--primary-blue)';
            }

            // 添加拖拽事件监听器
            setupDragEvents(snippetDiv, snippetData.index);

            // 添加手势操作
            setupGestureEvents(snippetDiv, snippetData.index);

            // 添加拖拽手柄
            const dragHandle = document.createElement('div');
            dragHandle.classList.add('drag-handle');
            dragHandle.innerHTML = '⋮⋮';
            dragHandle.title = '拖拽排序';

            const snippetContent = document.createElement('div');
            snippetContent.classList.add('snippet-content');
            snippetContent.innerHTML = processUrls(snippetData.content);

                // 批量模式下添加复选框
                if (batchMode) {
                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.checked = selectedIndices.has(snippetData.index);
                    checkbox.style.cssText = 'margin-right: 10px; cursor: pointer;';
                    checkbox.addEventListener('change', () => {
                        toggleSnippetSelection(snippetData.index);
                    });
                    
                    snippetContent.insertBefore(checkbox, snippetContent.firstChild);
                    
                    // 点击整个content区域也能选择
                    snippetContent.addEventListener('click', function(event) {
                        if (event.target === checkbox) return; // 避免双重触发
                        toggleSnippetSelection(snippetData.index);
                        checkbox.checked = selectedIndices.has(snippetData.index);
                    });
                } else {
                    // 非批量模式下的事件监听
                    snippetContent.addEventListener('click', function(event) {
                        // 检查点击的是否是链接或其他交互元素
                        if (event.target.tagName === 'A' || event.target.closest('a')) {
                            return; // 让链接正常工作
                        }
                        event.preventDefault();
                        event.stopPropagation();
                        performSmartCopy(event, snippetData.content);
                    });

                    // 鼠标移动时高亮当前片段
                    snippetContent.addEventListener('mousemove', function(event) {
                        highlightHoverSegment(event, snippetData.content, this);
                    });

                    // 鼠标离开时清除高亮
                    snippetContent.addEventListener('mouseleave', function() {
                        clearHoverHighlight(this);
                    });
                }

                const actions = document.createElement('div');
                actions.classList.add('actions');

                // 创建按钮
                const buttons = [
                    {
                        text: snippetData.pinned ? '🔓' : '📌',
                        class: snippetData.pinned ? 'pin-button pinned' : 'pin-button',
                        handler: function(e) { 
                            e.stopPropagation(); 
                            togglePin(snippetData.index); 
                        }
                    },
                    {
                        text: isAllExpanded ? '折叠 ▲' : '展开 ▼',
                        class: 'toggle-expand-button',
                        handler: function(e) {
                            e.stopPropagation();
                            toggleExpand(e.target, snippetContent);
                        }
                    },
                    {
                        text: '编辑',
                        class: 'edit-button',
                        handler: function(e) { 
                            e.stopPropagation(); 
                            editSnippet(snippetData.index); 
                        }
                    },
                    {
                        text: '复制',
                        class: 'copy-button',
                        handler: function(e) { 
                            e.stopPropagation(); 
                            copySnippet(snippetData.index); 
                        }
                    },

                    {
                        text: '删除',
                        class: 'delete-button',
                        handler: function(e) {
                            e.stopPropagation();
                            deleteSnippet(snippetData.index);
                        }
                    }
                ];

                buttons.forEach(btn => {
                    const button = document.createElement('button');
                    button.innerHTML = btn.text;
                    button.className = btn.class;
                    button.onclick = function(e) {
                        addButtonClickAnimation(button);
                        btn.handler(e);
                    };
                    actions.appendChild(button);
                });

                // 添加分区选择下拉菜单
                const sectionSelectContainer = document.createElement('div');
                sectionSelectContainer.style.cssText = 'margin-left: 8px;';
                
                const sectionSelect = document.createElement('select');
                sectionSelect.className = 'section-selector';
                sectionSelect.value = snippetData.section || '默认';
                
                // 添加所有分区选项
                allSections.forEach(section => {
                    const option = document.createElement('option');
                    option.value = section;
                    option.textContent = section;
                    if (section === (snippetData.section || '默认')) {
                        option.selected = true;
                    }
                    sectionSelect.appendChild(option);
                });

                sectionSelect.addEventListener('change', function(e) {
                    e.stopPropagation();
                    moveToSection(snippetData.index, this.value);
                });

                sectionSelectContainer.appendChild(sectionSelect);
                actions.appendChild(sectionSelectContainer);

                // 显示标签
                if (snippetData.tags && snippetData.tags.length > 0) {
                    const tagsContainer = document.createElement('div');
                    snippetData.tags.forEach(tag => {
                        const tagEl = document.createElement('span');
                        tagEl.classList.add('tag');
                        tagEl.textContent = tag;
                        tagsContainer.appendChild(tagEl);
                    });
                    snippetContent.appendChild(tagsContainer);
                }

                // 显示分区信息（可控制显示）
                if (showSectionInfo && snippetData.section && snippetData.section !== '默认') {
                    const sectionEl = document.createElement('div');
                    sectionEl.textContent = `分区: ${snippetData.section}`;
                    sectionEl.style.fontSize = '12px';
                    sectionEl.style.color = 'var(--text-medium)';
                    sectionEl.style.marginTop = '5px';
                    snippetContent.appendChild(sectionEl);
                }

                // 在全部视图中显示快速分区操作（可控制显示）
                if (showQuickSections && currentSection === 'all') {
                    const quickSectionDiv = document.createElement('div');
                    quickSectionDiv.style.cssText = 'margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee;';
                    
                    const quickLabel = document.createElement('span');
                    quickLabel.textContent = '快速分区: ';
                    quickLabel.style.cssText = 'font-size: 12px; color: var(--text-medium); margin-right: 8px;';
                    
                    quickSectionDiv.appendChild(quickLabel);
                    
                    // 为每个分区创建快速按钮
                    allSections.forEach(section => {
                        if (section !== (snippetData.section || '默认')) {
                            const quickBtn = document.createElement('button');
                            quickBtn.textContent = section;
                            quickBtn.style.cssText = 'font-size: 11px; padding: 2px 6px; margin: 0 3px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;';
                            quickBtn.onclick = (e) => {
                                e.stopPropagation();
                                moveToSection(snippetData.index, section);
                            };
                            quickBtn.onmouseover = () => {
                                quickBtn.style.background = 'var(--highlight-color)';
                                quickBtn.style.color = 'white';
                            };
                            quickBtn.onmouseout = () => {
                                quickBtn.style.background = '#f0f0f0';
                                quickBtn.style.color = 'black';
                            };
                            quickSectionDiv.appendChild(quickBtn);
                        }
                    });
                    
                    snippetContent.appendChild(quickSectionDiv);
                }

                snippetDiv.appendChild(dragHandle);
                snippetDiv.appendChild(snippetContent);
                snippetDiv.appendChild(actions);

                return snippetDiv;
        }

        // 添加分页控制
        function addPaginationControls(totalItems) {
            const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
            if (totalPages <= 1) return;

            const paginationDiv = document.createElement('div');
            paginationDiv.className = 'pagination-controls';
            paginationDiv.style.cssText = 'text-align: center; margin: 20px 0; padding: 15px;';

            // 上一页按钮
            if (currentPage > 0) {
                const prevBtn = document.createElement('button');
                prevBtn.textContent = '← 上一页';
                prevBtn.onclick = () => {
                    currentPage--;
                    displaySnippetsWithPagination(allSnippetsData);
                };
                prevBtn.style.cssText = 'margin: 0 5px; padding: 8px 16px; background: var(--button-primary); color: white; border: none; border-radius: 6px; cursor: pointer;';
                paginationDiv.appendChild(prevBtn);
            }

            // 页码信息
            const pageInfo = document.createElement('span');
            pageInfo.textContent = `第 ${currentPage + 1} 页，共 ${totalPages} 页`;
            pageInfo.style.cssText = 'margin: 0 15px; color: var(--text-medium);';
            paginationDiv.appendChild(pageInfo);

            // 下一页按钮
            if (currentPage < totalPages - 1) {
                const nextBtn = document.createElement('button');
                nextBtn.textContent = '下一页 →';
                nextBtn.onclick = () => {
                    currentPage++;
                    displaySnippetsWithPagination(allSnippetsData);
                };
                nextBtn.style.cssText = 'margin: 0 5px; padding: 8px 16px; background: var(--button-primary); color: white; border: none; border-radius: 6px; cursor: pointer;';
                paginationDiv.appendChild(nextBtn);
            }

            snippetsDiv.appendChild(paginationDiv);
        }

        // 添加无限滚动
        function addInfiniteScroll(snippets) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && currentPage < Math.ceil(snippets.length / ITEMS_PER_PAGE) - 1) {
                        currentPage++;
                        const startIndex = currentPage * ITEMS_PER_PAGE;
                        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, snippets.length);
                        const newPageSnippets = snippets.slice(startIndex, endIndex);

                        const fragment = document.createDocumentFragment();
                        newPageSnippets.forEach(snippetData => {
                            const snippetDiv = createSnippetElement(snippetData);
                            fragment.appendChild(snippetDiv);
                        });

                        // 移除分页控制，添加新内容，重新添加分页控制
                        const existingPagination = snippetsDiv.querySelector('.pagination-controls');
                        if (existingPagination) {
                            existingPagination.remove();
                        }

                        snippetsDiv.appendChild(fragment);
                        addPaginationControls(snippets.length);
                    }
                });
            }, { threshold: 0.1 });

            // 观察最后一个元素
            const lastElement = snippetsDiv.lastElementChild;
            if (lastElement) {
                observer.observe(lastElement);
            }
        }

        // 🚀 性能优化工具函数

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 节流函数
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }

        // 优化的搜索函数（使用防抖）
        const debouncedSearch = debounce(function(query) {
            const snippets = getSnippets();
            const filteredSnippets = snippets.filter(snippet => {
                const content = snippet.content.toLowerCase();
                const tags = (snippet.tags || []).join(' ').toLowerCase();
                const section = (snippet.section || '').toLowerCase();
                const searchQuery = query.toLowerCase();

                return content.includes(searchQuery) ||
                       tags.includes(searchQuery) ||
                       section.includes(searchQuery);
            });

            currentPage = 0; // 重置页码
            displaySnippets(filteredSnippets);
        }, 300);

        // 🎵 增强的内容处理 - 支持代码高亮和多媒体
        function processUrls(text) {
            let processedText = text;

            // 处理代码块
            processedText = processCodeBlocks(processedText);

            // 处理URL链接
            const urlRegex = /(https?:\/\/[^\s]+)/g;
            processedText = processedText.replace(urlRegex, '<a href="$1" target="_blank">$1</a>');

            return processedText;
        }

        // 代码块处理和高亮
        function processCodeBlocks(text) {
            // 检测代码块模式
            const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
            const inlineCodeRegex = /`([^`]+)`/g;

            // 处理多行代码块
            text = text.replace(codeBlockRegex, (match, language, code) => {
                const lang = language || 'text';
                const highlightedCode = highlightCode(code.trim(), lang);
                return `<div class="code-block" data-language="${lang}">${highlightedCode}</div>`;
            });

            // 处理行内代码
            text = text.replace(inlineCodeRegex, '<code style="background: #f1f3f4; padding: 2px 4px; border-radius: 3px; font-family: monospace; font-size: 0.9em;">$1</code>');

            return text;
        }

        // 简单的代码高亮
        function highlightCode(code, language) {
            let highlighted = code;

            if (language === 'javascript' || language === 'js') {
                // JavaScript 关键字
                const keywords = ['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'class', 'import', 'export', 'async', 'await'];
                keywords.forEach(keyword => {
                    const regex = new RegExp(`\\b${keyword}\\b`, 'g');
                    highlighted = highlighted.replace(regex, `<span class="code-keyword">${keyword}</span>`);
                });

                // 字符串
                highlighted = highlighted.replace(/(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="code-string">$1$2$1</span>');

                // 注释
                highlighted = highlighted.replace(/(\/\/.*$)/gm, '<span class="code-comment">$1</span>');
                highlighted = highlighted.replace(/(\/\*[\s\S]*?\*\/)/g, '<span class="code-comment">$1</span>');

                // 数字
                highlighted = highlighted.replace(/\b(\d+\.?\d*)\b/g, '<span class="code-number">$1</span>');
            } else if (language === 'python') {
                // Python 关键字
                const keywords = ['def', 'class', 'if', 'else', 'elif', 'for', 'while', 'return', 'import', 'from', 'as', 'try', 'except', 'with'];
                keywords.forEach(keyword => {
                    const regex = new RegExp(`\\b${keyword}\\b`, 'g');
                    highlighted = highlighted.replace(regex, `<span class="code-keyword">${keyword}</span>`);
                });

                // 字符串
                highlighted = highlighted.replace(/(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="code-string">$1$2$1</span>');

                // 注释
                highlighted = highlighted.replace(/(#.*$)/gm, '<span class="code-comment">$1</span>');
            } else if (language === 'html') {
                // HTML 标签
                highlighted = highlighted.replace(/(&lt;\/?)(\w+)([^&]*?)(&gt;)/g, '<span class="code-keyword">$1$2</span>$3<span class="code-keyword">$4</span>');

                // 属性
                highlighted = highlighted.replace(/(\w+)=("[^"]*")/g, '<span class="code-function">$1</span>=<span class="code-string">$2</span>');
            }

            return highlighted;
        }

        // 增强的文件处理
        function enhancedFilePreview(fileData) {
            const fileType = getFileType(fileData.name);
            const fileSize = formatFileSize(fileData.size);

            let preview = `
                <div class="file-preview">
                    <div class="file-preview-header">
                        <div class="file-icon">${getFileIcon(fileType)}</div>
                        <div class="file-info">
                            <div class="file-name">${fileData.name}</div>
                            <div class="file-size">${fileSize} • ${fileType.toUpperCase()}</div>
                        </div>
                        <div class="media-controls">
                            <button class="media-button" onclick="downloadFile(this.closest('.file-preview'))">📥 下载</button>
                        </div>
                    </div>
            `;

            // 根据文件类型添加预览
            if (fileType === 'image') {
                preview += `<img src="${fileData.data}" style="max-width: 100%; border-radius: 6px;" alt="${fileData.name}">`;
            } else if (fileType === 'audio') {
                preview += `<audio controls class="audio-player" src="${fileData.data}">您的浏览器不支持音频播放</audio>`;
            } else if (fileType === 'video') {
                preview += `<video controls style="max-width: 100%; border-radius: 6px;" src="${fileData.data}">您的浏览器不支持视频播放</video>`;
            } else if (fileType === 'text') {
                // 尝试显示文本内容
                try {
                    const textContent = atob(fileData.data.split(',')[1]);
                    preview += `<div style="background: white; padding: 10px; border-radius: 6px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; white-space: pre-wrap;">${textContent.substring(0, 1000)}${textContent.length > 1000 ? '...' : ''}</div>`;
                } catch (e) {
                    preview += `<div style="color: var(--text-medium); font-style: italic;">无法预览文本内容</div>`;
                }
            } else {
                preview += `<div style="color: var(--text-medium); font-style: italic;">此文件类型暂不支持预览</div>`;
            }

            preview += '</div>';
            return preview;
        }

        function getFileType(filename) {
            const ext = filename.split('.').pop().toLowerCase();

            if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)) return 'image';
            if (['mp3', 'wav', 'ogg', 'm4a'].includes(ext)) return 'audio';
            if (['mp4', 'webm', 'ogg'].includes(ext)) return 'video';
            if (['txt', 'md', 'js', 'html', 'css', 'json', 'xml'].includes(ext)) return 'text';
            if (['pdf'].includes(ext)) return 'pdf';
            if (['doc', 'docx'].includes(ext)) return 'document';
            if (['zip', 'rar', '7z'].includes(ext)) return 'archive';

            return 'unknown';
        }

        function getFileIcon(fileType) {
            const icons = {
                'image': '🖼️',
                'audio': '🎵',
                'video': '🎬',
                'text': '📝',
                'pdf': '📄',
                'document': '📋',
                'archive': '📦',
                'unknown': '📁'
            };
            return icons[fileType] || '📁';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 音频录制功能
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;

        function startAudioRecording() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                showSuccessMessage('❌ 您的浏览器不支持音频录制');
                return;
            }

            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(stream => {
                    mediaRecorder = new MediaRecorder(stream);
                    audioChunks = [];

                    mediaRecorder.ondataavailable = event => {
                        audioChunks.push(event.data);
                    };

                    mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        const audioUrl = URL.createObjectURL(audioBlob);

                        // 将音频添加到当前编辑的内容中
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            currentFileData = {
                                name: `录音_${new Date().toLocaleString()}.wav`,
                                size: audioBlob.size,
                                type: 'audio/wav',
                                data: e.target.result
                            };

                            // 更新文件名显示
                            const fileNameElement = document.getElementById('fileNameTop');
                            if (fileNameElement) {
                                fileNameElement.textContent = currentFileData.name;
                            }

                            showSuccessMessage('🎵 录音完成！');
                        };
                        reader.readAsDataURL(audioBlob);

                        // 停止所有音频轨道
                        stream.getTracks().forEach(track => track.stop());
                    };

                    mediaRecorder.start();
                    isRecording = true;
                    showSuccessMessage('🎤 开始录音...');

                    // 更新录音按钮状态
                    updateRecordingButton(true);
                })
                .catch(error => {
                    console.error('录音失败:', error);
                    showSuccessMessage('❌ 录音失败，请检查麦克风权限');
                });
        }

        function stopAudioRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                updateRecordingButton(false);
            }
        }

        function updateRecordingButton(recording) {
            const button = document.getElementById('recordButton');
            if (button) {
                if (recording) {
                    button.innerHTML = '⏹️';
                    button.style.background = '#dc3545';
                    button.title = '停止录音';
                } else {
                    button.innerHTML = '🎤';
                    button.style.background = 'var(--button-warning)';
                    button.title = '开始录音';
                }
            }
        }

        function toggleRecording() {
            if (isRecording) {
                stopAudioRecording();
            } else {
                startAudioRecording();
            }
        }

        // 添加snippet
        function addSnippet(position) {
            // 🔧 检查是否为编辑模式
            if (editingIndex !== null) {
                updateSnippet();
                return;
            }

            let snippet = newSnippetTextareaTop.value.trim();
            let image = currentImageBase64;
            let fileData = currentFileData;
            let tags = [...currentTags];
            let section = document.getElementById('sectionSelect-top').value || '默认';

            if (snippet === '' && image === '' && !fileData) return;

            let content = '';
            if (image !== '') {
                content += `<img src="${image}" style="max-width: 100px; max-height: 100px;"><br>`;
            }
            if (fileData) {
                content += `<div class="file-attachment" data-file='${JSON.stringify(fileData)}'>
                    📎 <a href="#" onclick="downloadFile(this.parentElement); event.preventDefault();">${fileData.name}</a>
                </div><br>`;
            }
            content += snippet;

            const newSnippet = {
                content,
                timestamp: new Date().getTime(),
                index: getSnippets().length,
                tags: tags,
                pinned: false,
                section: section
            };

            // 清空输入
            newSnippetTextareaTop.value = '';
            imagePreviewTop.style.display = "none";
            fileNameTop.textContent = '';
            currentImageBase64 = '';
            currentFileData = null;
            currentTags = [];

            const snippets = getSnippets();

            // 保存撤销状态
            saveStateForUndo('add', { snippet: newSnippet });

            snippets.push(newSnippet);

            if (saveSnippets(snippets)) {
                showSuccessMessage('添加成功！');
                loadSnippets();
            }
        }

        // 🆕 新增更新snippet函数
        function updateSnippet() {
            let snippet = newSnippetTextareaTop.value.trim();
            let image = currentImageBase64;
            let fileData = currentFileData;
            let tags = [...currentTags];
            let section = document.getElementById('sectionSelect-top').value || '默认';

            if (snippet === '' && image === '' && !fileData) return;

            let content = '';
            if (image !== '') {
                content += `<img src="${image}" style="max-width: 100px; max-height: 100px;"><br>`;
            }
            if (fileData) {
                content += `<div class="file-attachment" data-file='${JSON.stringify(fileData)}'>
                    📎 <a href="#" onclick="downloadFile(this.parentElement); event.preventDefault();">${fileData.name}</a>
                </div><br>`;
            }
            content += snippet;

            const snippets = getSnippets();
            const snippetIndex = snippets.findIndex(s => s.index === editingIndex);

            if (snippetIndex !== -1) {
                // 保存撤销状态
                saveStateForUndo('edit', {
                    oldSnippet: {...snippets[snippetIndex]},
                    newSnippet: {
                        content,
                        timestamp: snippets[snippetIndex].timestamp, // 保持原时间戳
                        index: editingIndex,
                        tags: tags,
                        pinned: snippets[snippetIndex].pinned, // 保持置顶状态
                        section: section
                    }
                });

                // 更新snippet
                snippets[snippetIndex] = {
                    content,
                    timestamp: snippets[snippetIndex].timestamp, // 保持原时间戳
                    index: editingIndex,
                    tags: tags,
                    pinned: snippets[snippetIndex].pinned, // 保持置顶状态
                    section: section
                };

                // 清空输入和重置编辑状态
                newSnippetTextareaTop.value = '';
                imagePreviewTop.style.display = "none";
                fileNameTop.textContent = '';
                currentImageBase64 = '';
                currentFileData = null;
                currentTags = [];
                editingIndex = null;

                // 🔧 重置按钮文本
                updateAddButtonText();

                // 隐藏高级功能面板
                document.getElementById('advanced-features-top').style.display = 'none';

                if (saveSnippets(snippets)) {
                    showSuccessMessage('修改成功！');
                    loadSnippets();
                }
            }
        }

        // 复制snippet
        function copySnippet(index) {
            const snippets = getSnippets();
            const snippetData = snippets.find(s => s.index === index);
            if (snippetData) {
                const textToCopy = snippetData.content.replace(/<[^>]+>/g, '');
                navigator.clipboard.writeText(textToCopy)
                    .then(() => showSuccessMessage('复制成功！'))
                    .catch(() => alert('复制失败'));
            }
        }

        // 删除snippet (支持撤销)
        function deleteSnippet(index) {
            const snippets = getSnippets();
            const snippetToDelete = snippets.find(s => s.index === index);

            if (snippetToDelete && confirm('确定要删除这条记录吗？')) {
                // 保存撤销状态
                saveStateForUndo('delete', { snippet: snippetToDelete, index: index });

                let newSnippets = snippets.filter(s => s.index !== index);
                newSnippets.forEach((s, i) => s.index = i);

                if (saveSnippets(newSnippets)) {
                    showSuccessMessage('删除成功！');
                    loadSnippets();
                }
            }
        }

        // 编辑snippet
        function editSnippet(index) {
            const snippets = getSnippets();
            const snippetData = snippets.find(s => s.index === index);
            if (!snippetData) return;

            editingIndex = index;
            newSnippetTextareaTop.value = snippetData.content.replace(/<[^>]+>/g, '');
            currentTags = snippetData.tags || [];

            // 设置分区
            document.getElementById('sectionSelect-top').value = snippetData.section || '默认';

            // 展开高级功能
            document.getElementById('advanced-features-top').style.display = 'block';

            // 🔧 更新按钮文本为编辑模式
            updateAddButtonText();

            newSnippetTextareaTop.focus();
        }

        // 🆕 更新添加按钮文本的函数
        function updateAddButtonText() {
            const addButton = document.querySelector('.add-button');
            if (!addButton) return; // 如果按钮不存在，直接返回

            // 确保 editingIndex 是正确的类型
            if (editingIndex !== null && editingIndex !== undefined) {
                addButton.innerHTML = '💾 保存修改';
                addButton.style.background = 'linear-gradient(135deg, var(--button-warning), #e67e22)';
            } else {
                addButton.innerHTML = '➕ 添加';
                addButton.style.background = 'linear-gradient(135deg, var(--button-primary), #0066cc)';
            }
        }

        // 🆕 取消编辑函数
        function cancelEdit() {
            editingIndex = null;
            newSnippetTextareaTop.value = '';
            imagePreviewTop.style.display = "none";
            fileNameTop.textContent = '';
            currentImageBase64 = '';
            currentFileData = null;
            currentTags = [];

            // 隐藏高级功能面板
            document.getElementById('advanced-features-top').style.display = 'none';

            // 重置按钮文本
            updateAddButtonText();

            showSuccessMessage('已取消编辑');
        }

        // 切换置顶
        function togglePin(index) {
            let snippets = getSnippets();
            const snippetIndex = snippets.findIndex(s => s.index === index);
            if (snippetIndex !== -1) {
                snippets[snippetIndex].pinned = !snippets[snippetIndex].pinned;
                if (saveSnippets(snippets)) {
                    showSuccessMessage(snippets[snippetIndex].pinned ? '已置顶' : '已取消置顶');
                    loadSnippets();
                }
            }
        }

        // 移动条目到指定分区
        function moveToSection(index, newSection) {
            let snippets = getSnippets();
            const snippetIndex = snippets.findIndex(s => s.index === index);
            if (snippetIndex !== -1) {
                const oldSection = snippets[snippetIndex].section || '默认';
                snippets[snippetIndex].section = newSection;
                if (saveSnippets(snippets)) {
                    showSuccessMessage(`条目已从 "${oldSection}" 移动到 "${newSection}"`);
                    loadSnippets();
                }
            }
        }

        // 切换展开/折叠
        function toggleExpand(button, content) {
            if (content.style.maxHeight === '80px') {
                content.style.maxHeight = 'none';
                button.innerHTML = '折叠 ▲';
            } else {
                content.style.maxHeight = '80px';
                content.style.overflow = 'hidden';
                button.innerHTML = '展开 ▼';
            }
        }

        // 全部展开/折叠
        function toggleAllExpand() {
            isAllExpanded = !isAllExpanded;
            const toggleBtn = document.getElementById('toggleAllExpandBtn');
            
            if (isAllExpanded) {
                toggleBtn.innerHTML = '📤 全部折叠';
                document.querySelectorAll('.snippet-content').forEach(content => {
                    content.style.maxHeight = 'none';
                });
                document.querySelectorAll('.toggle-expand-button').forEach(btn => {
                    btn.innerHTML = '折叠 ▲';
                });
            } else {
                toggleBtn.innerHTML = '📥 全部展开';
                document.querySelectorAll('.snippet-content').forEach(content => {
                    content.style.maxHeight = '80px';
                    content.style.overflow = 'hidden';
                });
                document.querySelectorAll('.toggle-expand-button').forEach(btn => {
                    btn.innerHTML = '展开 ▼';
                });
            }
        }

        // 智能复制
        function performSmartCopy(event, content) {
            const contentElement = event.target.closest('.snippet-content');
            if (!contentElement) return;
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            let textContent = tempDiv.textContent || tempDiv.innerText || '';
            
            // 按空格、逗号、顿号分隔文本，过滤空值
            const segments = textContent.split(/[,，、\s]+/).filter(s => s.trim().length > 0);
            
            if (segments.length === 0) return;
            
            // 获取点击位置相对于文本的偏移
            const range = document.caretRangeFromPoint(event.clientX, event.clientY);
            let clickOffset = 0;
            
            if (range && range.startContainer) {
                // 计算相对于整个文本内容的偏移
                const walker = document.createTreeWalker(
                    contentElement,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                let totalOffset = 0;
                let node;
                while (node = walker.nextNode()) {
                    if (node === range.startContainer) {
                        clickOffset = totalOffset + range.startOffset;
                        break;
                    }
                    totalOffset += node.textContent.length;
                }
            }
            
            // 在原文本中找到对应的片段
            let currentOffset = 0;
            let selectedSegment = segments[0]; // 默认选择第一个
            
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const segmentStart = textContent.indexOf(segment, currentOffset);
                const segmentEnd = segmentStart + segment.length;
                
                if (clickOffset >= segmentStart && clickOffset <= segmentEnd) {
                    selectedSegment = segment;
                    break;
                }
                currentOffset = segmentEnd;
            }
            
            // 清理文本：去掉逗号和多余空格
            const cleanText = selectedSegment.replace(/[,，]/g, '').trim();
            
            if (cleanText) {
                navigator.clipboard.writeText(cleanText)
                    .then(() => {
                        showSuccessMessage(`✅ 智能复制: "${cleanText}"`);
                        highlightTextSegment(contentElement, selectedSegment);
                    })
                    .catch(() => {
                        showSuccessMessage('❌ 复制失败');
                    });
            }
        }

        // 精确高亮被复制的文本片段
        function highlightTextSegment(contentElement, segment) {
            const originalHTML = contentElement.innerHTML;
            
            // 创建高亮的HTML
            const highlightedHTML = originalHTML.replace(
                new RegExp(`(${escapeRegExp(segment)})`, 'gi'),
                '<span class="highlighted-text">$1</span>'
            );
            
            contentElement.innerHTML = highlightedHTML;
            
            // 0.8秒后恢复原样
            setTimeout(() => {
                contentElement.innerHTML = originalHTML;
            }, 800);
        }

        // 转义正则表达式特殊字符
        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // 排序功能
        function sortSnippetsData(snippets, sortType) {
            return [...snippets].sort((a, b) => {
                // 先按置顶排序
                if (a.pinned !== b.pinned) {
                    return b.pinned - a.pinned;
                }
                
                switch (sortType) {
                    case 'date-asc':
                        return a.timestamp - b.timestamp;
                    case 'date-desc':
                        return b.timestamp - a.timestamp;
                    case 'text-asc':
                        return a.content.localeCompare(b.content);
                    case 'text-desc':
                        return b.content.localeCompare(a.content);
                    case 'original':
                    default:
                        // 按原始顺序排序（最新的在前面）
                        return (a.originalIndex !== undefined ? a.originalIndex : a.index) - 
                            (b.originalIndex !== undefined ? b.originalIndex : b.index);
                }
            });
        }

        function sortSnippets(sortType) {
            currentSort = sortType;
            // 保存排序状态到localStorage
            localStorage.setItem('currentSort', sortType);
            loadSnippets();
        }

        // 增强搜索功能 - 支持内容、标签、分区搜索（使用防抖优化）
        function searchSnippets() {
            const query = document.getElementById('searchInput').value;
            currentSearchTerm = query.toLowerCase();

            if (query.trim() === '') {
                // 如果搜索为空，显示所有snippets
                currentPage = 0;
                loadSnippets();
            } else {
                // 使用防抖搜索
                debouncedSearch(query);
            }
        }

        // 增强的搜索匹配函数
        function matchesSearch(snippet, searchTerm) {
            if (!searchTerm) return true;

            // 搜索内容
            const content = snippet.content.toLowerCase();
            if (content.includes(searchTerm)) return true;

            // 搜索标签
            if (snippet.tags && snippet.tags.length > 0) {
                const tagsMatch = snippet.tags.some(tag =>
                    tag.toLowerCase().includes(searchTerm)
                );
                if (tagsMatch) return true;
            }

            // 搜索分区
            if (snippet.section) {
                const sectionMatch = snippet.section.toLowerCase().includes(searchTerm);
                if (sectionMatch) return true;
            }

            return false;
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            currentSearchTerm = '';
            loadSnippets();
        }

        // 分区管理
        function switchSection(section) {
            currentSection = section;
            
            // 更新标签样式
            document.querySelectorAll('.section-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-section="${section}"]`).classList.add('active');
            
            loadSnippets();
        }

        function addNewSection() {
            const sectionName = document.getElementById('newSectionName').value.trim();
            if (sectionName && !allSections.has(sectionName)) {
                allSections.add(sectionName);
                document.getElementById('newSectionName').value = '';
                
                // 保存分区信息
                if (saveSections()) {
                    updateSectionTabs();
                    updateSectionSelects();
                    showSuccessMessage('分区添加成功！');
                    
                    // 如果当前在全部视图，询问是否要切换到新分区
                    if (currentSection === 'all' && confirm(`分区"${sectionName}"创建成功！\n是否切换到该分区并开始添加条目？`)) {
                        switchSection(sectionName);
                    } else if (currentSection !== 'all') {
                        // 如果不在全部视图，建议切换到全部视图选择条目
                        if (confirm(`分区"${sectionName}"创建成功！\n\n建议切换到"全部"视图，您可以看到每个条目下方的快速分区按钮，\n轻松将现有条目添加到新分区。\n\n是否现在切换到全部视图？`)) {
                            switchSection('all');
                            
                            // 短暂延迟后显示使用提示
                            setTimeout(() => {
                                showSuccessMessage('💡 现在您可以在每个条目下方看到快速分区按钮！');
                            }, 1000);
                        }
                    }
                } else {
                    allSections.delete(sectionName); // 保存失败时回滚
                    alert('分区保存失败');
                }
            } else if (allSections.has(sectionName)) {
                alert('分区名称已存在');
            }
        }

        // 分区标签更新和管理函数
        function updateSectionTabs() {
            const sectionTabs = document.getElementById('sectionTabs');
            const snippets = getSnippets();
            
            // 计算每个分区的数量
            const sectionCounts = {};
            sectionCounts['all'] = snippets.length;
            
            allSections.forEach(section => {
                sectionCounts[section] = snippets.filter(s => (s.section || '默认') === section).length;
            });

            sectionTabs.innerHTML = '';

            // 全部标签
            const allTab = document.createElement('div');
            allTab.className = `section-tab ${currentSection === 'all' ? 'active' : ''}`;
            allTab.dataset.section = 'all';
            allTab.innerHTML = `
                <span>全部</span>
                <span class="section-count">${sectionCounts['all']}</span>
            `;
            allTab.addEventListener('click', () => switchSection('all'));
            sectionTabs.appendChild(allTab);

            // 其他分区标签
            allSections.forEach(section => {
                const tab = document.createElement('div');
                tab.className = `section-tab ${currentSection === section ? 'active' : ''}`;
                tab.dataset.section = section;
                tab.innerHTML = `
                    <span>${section}</span>
                    <span class="section-count">${sectionCounts[section] || 0}</span>
                    ${section !== '默认' ? '<span class="remove-section" style="margin-left: 8px; color: #ff3b30; cursor: pointer;">×</span>' : ''}
                `;
                tab.addEventListener('click', (e) => {
                    if (e.target.classList.contains('remove-section')) {
                        e.stopPropagation();
                        removeSection(section);
                    } else {
                        switchSection(section);
                    }
                });
                sectionTabs.appendChild(tab);
            });
        }

        function updateSectionSelects() {
            const select = document.getElementById('sectionSelect-top');
            select.innerHTML = '';
            
            allSections.forEach(section => {
                const option = document.createElement('option');
                option.value = section;
                option.textContent = section;
                select.appendChild(option);
            });
            
            // 同时更新批量管理的分区选择器
            updateBatchTargetSection();
        }

        function removeSection(sectionName) {
            if (confirm(`确定要删除分区"${sectionName}"吗？该分区下的所有条目将移动到默认分区。`)) {
                let snippets = getSnippets();
                snippets = snippets.map(snippet => {
                    if (snippet.section === sectionName) {
                        snippet.section = '默认';
                    }
                    return snippet;
                });
                
                allSections.delete(sectionName);
                saveSnippets(snippets);
                saveSections(); // 同时保存分区信息
                
                if (currentSection === sectionName) {
                    currentSection = 'all';
                }
                
                loadSnippets();
                showSuccessMessage('分区删除成功！');
            }
        }

        function handleSectionInput(event) {
            if (event.key === 'Enter') {
                addNewSection();
            }
        }

        // 高级功能切换
        function toggleAdvancedFeatures(position) {
            const container = document.getElementById(`advanced-features-${position}`);
            if (container.style.display === 'none') {
                container.style.display = 'block';
            } else {
                container.style.display = 'none';
            }
        }

        // 标签处理
        function handleTagInput(event, position) {
            if (event.key === 'Enter') {
                const tagValue = event.target.value.trim();
                if (tagValue && !currentTags.includes(tagValue)) {
                    currentTags.push(tagValue);
                    event.target.value = '';
                    updateTagsDisplay(position);
                }
            }
        }

        function updateTagsDisplay(position) {
            const container = document.getElementById(`tags-display-${position}`);
            container.innerHTML = '';
            
            currentTags.forEach(tag => {
                const tagEl = document.createElement('span');
                tagEl.classList.add('tag');
                tagEl.textContent = tag;
                tagEl.onclick = () => removeTag(tag, position);
                container.appendChild(tagEl);
            });
        }

        function removeTag(tag, position) {
            currentTags = currentTags.filter(t => t !== tag);
            updateTagsDisplay(position);
        }

        // 深色模式切换
        function toggleDarkMode() {
            document.body.classList.toggle('dark-mode');
            const isDarkMode = document.body.classList.contains('dark-mode');
            localStorage.setItem('darkMode', isDarkMode ? 'enabled' : 'disabled');
            document.getElementById('darkModeIcon').textContent = isDarkMode ? '☀️' : '🌙';
        }

        // 导出数据
        function exportData() {
            const snippets = localStorage.getItem('snippets');
            if (!snippets) {
                alert('没有数据可导出');
                return;
            }

            const dataObj = {
                snippets: JSON.parse(snippets),
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(dataObj, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `网络剪贴板_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            
            showSuccessMessage('数据导出成功！');
        }

        // 导入数据
        function importData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    if (!data.snippets) {
                        throw new Error('导入的文件格式无效');
                    }

                    if (confirm('导入将覆盖当前数据，确定继续吗？')) {
                        localStorage.setItem('snippets', JSON.stringify(data.snippets));
                        loadSnippets();
                        showSuccessMessage('数据导入成功！');
                    }
                } catch (error) {
                    alert('导入失败: ' + error.message);
                }
            };
            reader.readAsText(file);
            event.target.value = '';
        }

        // 成功消息
        function showSuccessMessage(message) {
            const messageEl = document.createElement('div');
            messageEl.textContent = message;
            messageEl.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: var(--button-success);
                color: white;
                padding: 15px 30px;
                border-radius: 12px;
                z-index: 1000;
                box-shadow: 0 6px 16px rgba(0,0,0,0.2);
                animation: successMessage 1.8s ease forwards;
            `;

            const style = document.createElement('style');
            style.textContent = `
                @keyframes successMessage {
                    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    15% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
                    20% { transform: translate(-50%, -50%) scale(1); }
                    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                }
            `;

            document.head.appendChild(style);
            document.body.appendChild(messageEl);

            setTimeout(() => {
                document.body.removeChild(messageEl);
                document.head.removeChild(style);
            }, 1800);
        }

        // 下载文件
        function downloadFile(fileElement) {
            try {
                const fileData = JSON.parse(fileElement.dataset.file);
                const link = document.createElement('a');
                link.href = fileData.data;
                link.download = fileData.name;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                alert('下载失败: ' + error.message);
            }
        }

        // 数据恢复相关函数
        function showDataRecovery() {
            document.getElementById('dataRecoveryPanel').style.display = 'block';
        }

        function hideDataRecovery() {
            document.getElementById('dataRecoveryPanel').style.display = 'none';
        }

        function checkLocalStorageData() {
            const rawData = localStorage.getItem('snippets');
            if (rawData) {
                try {
                    const parsedData = JSON.parse(rawData);
                    alert(`找到 ${parsedData.length} 条本地数据记录\n\n点击"查看原始数据"可以看到详细内容`);
                } catch (error) {
                    alert('本地数据格式异常：' + error.message);
                }
            } else {
                alert('未找到本地数据，可能已被清空');
            }
        }

        function restoreFromBackup() {
            const backupKeys = ['snippets_backup', 'snippets_backup_auto', 'snippets_backup_before_import'];
            let restored = false;
            
            for (const key of backupKeys) {
                const backup = localStorage.getItem(key);
                if (backup) {
                    try {
                        const backupData = JSON.parse(backup);
                        if (confirm(`找到备份数据 ${backupData.length} 条，是否恢复？\n备份来源：${key}`)) {
                            localStorage.setItem('snippets', backup);
                            loadSnippets();
                            showSuccessMessage('数据恢复成功！');
                            hideDataRecovery();
                            restored = true;
                            break;
                        }
                    } catch (error) {
                        console.error(`备份 ${key} 损坏:`, error);
                    }
                }
            }
            
            if (!restored) {
                alert('未找到可用的备份数据');
            }
        }

        function showRawData() {
            const rawData = localStorage.getItem('snippets');
            if (rawData) {
                const newWindow = window.open('', '_blank');
                newWindow.document.write(`
                    <html>
                    <head><title>原始数据查看</title></head>
                    <body style="font-family: monospace; padding: 20px;">
                        <h2>localStorage中的原始数据：</h2>
                        <pre style="background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;">${rawData}</pre>
                    </body>
                    </html>
                `);
                newWindow.document.close();
            } else {
                alert('未找到本地数据');
            }
        }

        function clearAndReset() {
            if (confirm('⚠️ 警告：这将清除所有数据！\n\n确定要清空并重置吗？此操作不可撤销！')) {
                localStorage.removeItem('snippets');
                localStorage.removeItem('snippets_backup');
                localStorage.removeItem('snippets_backup_auto');
                loadSnippets();
                showSuccessMessage('数据已清空');
                hideDataRecovery();
            }
        }

        // 批量管理功能
        function toggleBatchMode() {
            batchMode = !batchMode;
            selectedIndices.clear();
            
            const btn = document.getElementById('batchModeBtn');
            const actions = document.getElementById('batchActions');
            const counter = document.getElementById('batchCounter');
            
            if (batchMode) {
                btn.textContent = '关闭批量选择';
                btn.style.background = '#ff9500';
                actions.style.display = 'flex';
            } else {
                btn.textContent = '开启批量选择';
                btn.style.background = 'var(--button-info)';
                actions.style.display = 'none';
            }
            
            counter.textContent = '已选择 0 项';
            loadSnippets();
        }

        function updateBatchTargetSection() {
            const select = document.getElementById('batchTargetSection');
            select.innerHTML = '<option value="">选择目标分区</option>';
            
            allSections.forEach(section => {
                const option = document.createElement('option');
                option.value = section;
                option.textContent = section;
                select.appendChild(option);
            });
        }

        function toggleSnippetSelection(index) {
            if (selectedIndices.has(index)) {
                selectedIndices.delete(index);
            } else {
                selectedIndices.add(index);
            }
            
            updateBatchCounter();
            updateSnippetSelectionUI(index);
        }

        function updateBatchCounter() {
            const counter = document.getElementById('batchCounter');
            counter.textContent = `已选择 ${selectedIndices.size} 项`;
        }

        function updateSnippetSelectionUI(index) {
            const snippet = document.querySelector(`[data-index="${index}"]`);
            if (snippet) {
                if (selectedIndices.has(index)) {
                    snippet.style.background = 'rgba(0, 123, 255, 0.1)';
                    snippet.style.border = '2px solid var(--highlight-color)';
                } else {
                    snippet.style.background = '';
                    snippet.style.border = '';
                }
            }
        }

        function batchMoveToSection() {
            const targetSection = document.getElementById('batchTargetSection').value;
            if (!targetSection) {
                alert('请选择目标分区');
                return;
            }
            
            if (selectedIndices.size === 0) {
                alert('请先选择要移动的条目');
                return;
            }
            
            let snippets = getSnippets();
            let moveCount = 0;
            
            selectedIndices.forEach(index => {
                const snippetIndex = snippets.findIndex(s => s.index === index);
                if (snippetIndex !== -1) {
                    snippets[snippetIndex].section = targetSection;
                    moveCount++;
                }
            });
            
            if (saveSnippets(snippets)) {
                showSuccessMessage(`成功移动 ${moveCount} 个条目到 "${targetSection}"`);
                selectedIndices.clear();
                loadSnippets();
                updateBatchCounter();
            }
        }

        function batchDelete() {
            if (selectedIndices.size === 0) {
                alert('请先选择要删除的条目');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${selectedIndices.size} 个条目吗？此操作不可撤销！`)) {
                let snippets = getSnippets();
                snippets = snippets.filter(s => !selectedIndices.has(s.index));
                
                // 重新编号
                snippets.forEach((s, i) => s.index = i);
                
                if (saveSnippets(snippets)) {
                    showSuccessMessage(`成功删除 ${selectedIndices.size} 个条目`);
                    selectedIndices.clear();
                    loadSnippets();
                    updateBatchCounter();
                }
            }
        }

        // 通用折叠功能 - 简化版本
        function toggleCollapsibleSection(sectionName) {
            const content = document.getElementById(sectionName + 'Content');
            const toggle = document.getElementById(sectionName + 'Toggle');

            if (!content || !toggle) {
                console.warn(`折叠切换失败: 元素未找到 ${sectionName}`);
                return;
            }

            const isHidden = content.style.display === 'none';

            if (isHidden) {
                // 展开
                content.style.display = 'block';
                toggle.textContent = '🔽';
                localStorage.setItem(sectionName + 'Collapsed', 'false');
                console.log(`${sectionName} 已展开，保存状态: false`);
            } else {
                // 折叠
                content.style.display = 'none';
                toggle.textContent = '🔼';
                localStorage.setItem(sectionName + 'Collapsed', 'true');
                console.log(`${sectionName} 已折叠，保存状态: true`);
            }
        }

        // 初始化所有折叠区域 - 修复版本
        function initCollapsibleSections() {
            console.log('=== 开始初始化折叠状态 ===');

            // 直接处理toolbox
            const toolboxContent = document.getElementById('toolboxContent');
            const toolboxToggle = document.getElementById('toolboxToggle');
            const toolboxSaved = localStorage.getItem('toolboxCollapsed');

            console.log(`toolbox localStorage值: "${toolboxSaved}"`);
            console.log(`toolbox 元素存在: content=${!!toolboxContent}, toggle=${!!toolboxToggle}`);

            if (toolboxContent && toolboxToggle) {
                // 修复逻辑：只有明确保存为'true'时才折叠，否则默认展开
                if (toolboxSaved === 'true') {
                    // 折叠
                    toolboxContent.style.display = 'none';
                    toolboxToggle.textContent = '🔼';
                    console.log('toolbox 恢复为折叠状态');
                } else {
                    // 展开（包括首次访问和明确设置为展开的情况）
                    toolboxContent.style.display = 'block';
                    toolboxToggle.textContent = '🔽';
                    // 如果是首次访问，设置默认状态为展开
                    if (toolboxSaved === null) {
                        localStorage.setItem('toolboxCollapsed', 'false');
                        console.log('toolbox 首次访问，默认设置为展开');
                    } else {
                        console.log('toolbox 恢复为展开状态');
                    }
                }
            }

            // 直接处理addContent
            const addContent = document.getElementById('addContentContent');
            const addToggle = document.getElementById('addContentToggle');
            const addSaved = localStorage.getItem('addContentCollapsed');

            console.log(`addContent localStorage值: "${addSaved}"`);
            console.log(`addContent 元素存在: content=${!!addContent}, toggle=${!!addToggle}`);

            if (addContent && addToggle) {
                if (addSaved === 'true') {
                    // 折叠
                    addContent.style.display = 'none';
                    addToggle.textContent = '🔼';
                    console.log('addContent 恢复为折叠状态');
                } else {
                    // 展开（包括首次访问和明确设置为展开的情况）
                    addContent.style.display = 'block';
                    addToggle.textContent = '🔽';
                    // 如果是首次访问，设置默认状态为展开
                    if (addSaved === null) {
                        localStorage.setItem('addContentCollapsed', 'false');
                        console.log('addContent 首次访问，默认设置为展开');
                    } else {
                        console.log('addContent 恢复为展开状态');
                    }
                }
            }

            console.log('=== 折叠状态初始化完成 ===');
        }

        // 显示控制功能
        function toggleSectionInfo() {
            showSectionInfo = !showSectionInfo;
            localStorage.setItem('showSectionInfo', showSectionInfo.toString());
            loadSnippets();
        }

        function toggleQuickSections() {
            showQuickSections = !showQuickSections;
            localStorage.setItem('showQuickSections', showQuickSections.toString());
            loadSnippets();
        }

        // 初始化显示设置
        function initDisplaySettings() {
            // 从localStorage加载设置
            showSectionInfo = localStorage.getItem('showSectionInfo') === 'true';
            showQuickSections = localStorage.getItem('showQuickSections') === 'true';
            
            // 设置复选框状态
            const sectionInfoCheck = document.getElementById('showSectionInfoCheck');
            const quickSectionsCheck = document.getElementById('showQuickSectionsCheck');
            
            if (sectionInfoCheck) sectionInfoCheck.checked = showSectionInfo;
            if (quickSectionsCheck) quickSectionsCheck.checked = showQuickSections;
        }

        // 悬停时高亮当前文本片段
        function highlightHoverSegment(event, content, contentElement) {
            // 清除之前的高亮
            clearHoverHighlight(contentElement);
            
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            let textContent = tempDiv.textContent || tempDiv.innerText || '';
            
            // 按空格、逗号、顿号分隔文本
            const segments = textContent.split(/[,，、\s]+/).filter(s => s.trim().length > 0);
            if (segments.length === 0) return;
            
            // 获取点击位置
            const range = document.caretRangeFromPoint(event.clientX, event.clientY);
            let clickOffset = 0;
            
            if (range && range.startContainer) {
                const walker = document.createTreeWalker(
                    contentElement,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                let totalOffset = 0;
                let node;
                while (node = walker.nextNode()) {
                    if (node === range.startContainer) {
                        clickOffset = totalOffset + range.startOffset;
                        break;
                    }
                    totalOffset += node.textContent.length;
                }
            }
            
            // 找到对应的片段
            let currentOffset = 0;
            let selectedSegment = null;
            
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const segmentStart = textContent.indexOf(segment, currentOffset);
                const segmentEnd = segmentStart + segment.length;
                
                if (clickOffset >= segmentStart && clickOffset <= segmentEnd) {
                    selectedSegment = segment;
                    break;
                }
                currentOffset = segmentEnd;
            }
            
            // 高亮当前片段
            if (selectedSegment && selectedSegment.trim()) {
                const originalHTML = contentElement.innerHTML;
                const highlightedHTML = originalHTML.replace(
                    new RegExp(`(${escapeRegExp(selectedSegment)})`, 'gi'),
                    '<span class="hover-segment">$1</span>'
                );
                contentElement.innerHTML = highlightedHTML;
                contentElement.dataset.originalHtml = originalHTML;
            }
        }

        // 清除悬停高亮
        function clearHoverHighlight(contentElement) {
            if (contentElement.dataset.originalHtml) {
                contentElement.innerHTML = contentElement.dataset.originalHtml;
                delete contentElement.dataset.originalHtml;
            }
        }

        // ⌨️ 键盘快捷键支持
        function initKeyboardShortcuts() {
            document.addEventListener('keydown', function(event) {
                // Ctrl + Enter: 快速添加
                if (event.ctrlKey && event.key === 'Enter') {
                    event.preventDefault();
                    if (newSnippetTextareaTop.value.trim()) {
                        addSnippet('top');
                    } else {
                        newSnippetTextareaTop.focus();
                    }
                }

                // Ctrl + F: 搜索
                if (event.ctrlKey && event.key === 'f') {
                    event.preventDefault();
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                }

                // Ctrl + D: 切换深色模式
                if (event.ctrlKey && event.key === 'd') {
                    event.preventDefault();
                    toggleDarkMode();
                }

                // Ctrl + A: 全选（在批量模式下）
                if (event.ctrlKey && event.key === 'a' && batchMode) {
                    event.preventDefault();
                    selectAllSnippets();
                }

                // Ctrl + B: 切换批量模式
                if (event.ctrlKey && event.key === 'b') {
                    event.preventDefault();
                    toggleBatchMode();
                }

                // Delete: 删除选中的项目
                if (event.key === 'Delete') {
                    if (batchMode && selectedIndices.size > 0) {
                        event.preventDefault();
                        batchDelete();
                    }
                }

                // Ctrl + Z: 撤销
                if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
                    event.preventDefault();
                    undo();
                }

                // Ctrl + Shift + Z 或 Ctrl + Y: 重做
                if (event.ctrlKey && ((event.key === 'z' && event.shiftKey) || event.key === 'y')) {
                    event.preventDefault();
                    redo();
                }

                // Ctrl + S: 导出数据
                if (event.ctrlKey && event.key === 's') {
                    event.preventDefault();
                    exportData();
                }

                // Escape: 退出批量模式或清除搜索
                if (event.key === 'Escape') {
                    if (batchMode) {
                        toggleBatchMode();
                    } else if (currentSearchTerm) {
                        clearSearch();
                    } else if (editingIndex !== null) {
                        // 🔧 取消编辑
                        cancelEdit();
                    }
                }

                // F1: 显示/隐藏快捷键提示
                if (event.key === 'F1') {
                    event.preventDefault();
                    toggleKeyboardHint();
                }

                // 数字键 1-9: 快速切换分区
                if (event.ctrlKey && event.key >= '1' && event.key <= '9') {
                    event.preventDefault();
                    const sectionIndex = parseInt(event.key) - 1;
                    const sections = [...allSections];
                    if (sectionIndex < sections.length) {
                        switchSection(sections[sectionIndex]);
                        showSuccessMessage(`切换到分区: ${sections[sectionIndex]}`);
                    }
                }
            });
        }

        // 显示键盘快捷键提示
        function showKeyboardHint() {
            const hint = document.getElementById('keyboardHint');
            if (hint) {
                hint.classList.add('show');
            }
        }

        // 隐藏键盘快捷键提示
        function hideKeyboardHint() {
            const hint = document.getElementById('keyboardHint');
            if (hint) {
                hint.classList.remove('show');
            }
        }

        // 切换键盘快捷键提示显示状态
        function toggleKeyboardHint() {
            const hint = document.getElementById('keyboardHint');
            if (hint) {
                hint.classList.toggle('show');
            }
        }

        // 全选所有片段（批量模式下）
        function selectAllSnippets() {
            if (!batchMode) return;

            const snippets = getSnippets();
            let filteredSnippets = snippets;

            // 应用当前的过滤条件
            if (currentSection !== 'all') {
                filteredSnippets = snippets.filter(snippet =>
                    (snippet.section || '默认') === currentSection
                );
            }

            if (currentSearchTerm) {
                filteredSnippets = filteredSnippets.filter(snippet =>
                    snippet.content.toLowerCase().includes(currentSearchTerm)
                );
            }

            // 选择所有当前显示的片段
            filteredSnippets.forEach(snippet => {
                selectedIndices.add(snippet.index);
            });

            loadSnippets(); // 重新渲染以显示选择状态
        }

        // 清除搜索
        function clearSearch() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
                currentSearchTerm = '';
                loadSnippets();
            }
        }

        // 优化搜索功能 - 添加实时搜索
        function initEnhancedSearch() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                let searchTimeout;

                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        currentSearchTerm = this.value.toLowerCase();
                        loadSnippets();
                    }, 300); // 300ms 防抖
                });

                // 支持 Enter 键搜索
                searchInput.addEventListener('keydown', function(event) {
                    if (event.key === 'Enter') {
                        currentSearchTerm = this.value.toLowerCase();
                        loadSnippets();
                    }
                });
            }
        }

        // 在页面加载完成后初始化增强搜索
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initEnhancedSearch, 100);
        });

        // 🎯 拖拽排序功能
        function setupDragEvents(element, index) {
            element.addEventListener('dragstart', function(e) {
                draggedElement = element;
                draggedIndex = index;
                element.classList.add('dragging');

                // 设置拖拽数据
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', element.outerHTML);

                // 创建拖拽时的视觉反馈
                setTimeout(() => {
                    element.style.opacity = '0.5';
                }, 0);
            });

            element.addEventListener('dragend', function(e) {
                element.classList.remove('dragging');
                element.style.opacity = '';

                // 清理所有拖拽状态
                document.querySelectorAll('.snippet').forEach(snippet => {
                    snippet.classList.remove('drag-over');
                });

                draggedElement = null;
                draggedIndex = -1;
            });

            element.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                if (draggedElement && draggedElement !== element) {
                    element.classList.add('drag-over');
                }
            });

            element.addEventListener('dragleave', function(e) {
                element.classList.remove('drag-over');
            });

            element.addEventListener('drop', function(e) {
                e.preventDefault();
                element.classList.remove('drag-over');

                if (draggedElement && draggedElement !== element) {
                    const targetIndex = parseInt(element.dataset.index);
                    reorderSnippets(draggedIndex, targetIndex);
                }
            });
        }

        // 重新排序snippets
        function reorderSnippets(fromIndex, toIndex) {
            if (fromIndex === toIndex) return;

            let snippets = getSnippets();

            // 找到要移动的元素
            const fromSnippet = snippets.find(s => s.index === fromIndex);
            const toSnippet = snippets.find(s => s.index === toIndex);

            if (!fromSnippet || !toSnippet) return;

            // 移除要移动的元素
            snippets = snippets.filter(s => s.index !== fromIndex);

            // 找到目标位置并插入
            const targetPosition = snippets.findIndex(s => s.index === toIndex);
            snippets.splice(targetPosition, 0, fromSnippet);

            // 重新分配索引
            snippets.forEach((snippet, index) => {
                snippet.index = index;
            });

            // 保存并重新加载
            if (saveSnippets(snippets)) {
                showSuccessMessage('🎯 排序已更新！');
                loadSnippets();
            }
        }

        // 🎨 动画增强函数
        function addButtonClickAnimation(button) {
            button.classList.add('button-clicked');
            setTimeout(() => {
                button.classList.remove('button-clicked');
            }, 300);
        }

        function addErrorShakeAnimation(element) {
            element.classList.add('error-shake');
            setTimeout(() => {
                element.classList.remove('error-shake');
            }, 500);
        }

        function addSearchHighlight(element) {
            element.classList.add('search-highlight');
            setTimeout(() => {
                element.classList.remove('search-highlight');
            }, 2000);
        }

        // 增强的消息显示系统
        function showMessage(message, type = 'success', duration = 3000) {
            // 移除现有消息
            const existingMessage = document.querySelector('.toast-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // 创建消息元素
            const messageEl = document.createElement('div');
            messageEl.className = 'toast-message';
            messageEl.innerHTML = message;

            // 根据类型设置样式
            const styles = {
                success: {
                    background: 'linear-gradient(135deg, var(--success), #059669)',
                    icon: '✅'
                },
                error: {
                    background: 'linear-gradient(135deg, var(--danger), #dc2626)',
                    icon: '❌'
                },
                warning: {
                    background: 'linear-gradient(135deg, var(--warning), #d97706)',
                    icon: '⚠️'
                },
                info: {
                    background: 'linear-gradient(135deg, var(--info), #0284c7)',
                    icon: 'ℹ️'
                }
            };

            const style = styles[type] || styles.success;

            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${style.background};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                animation: slideInUp 0.3s ease-out;
                max-width: 300px;
                word-wrap: break-word;
                display: flex;
                align-items: center;
                gap: 8px;
            `;

            // 添加图标
            const icon = document.createElement('span');
            icon.textContent = style.icon;
            icon.style.fontSize = '16px';
            messageEl.insertBefore(icon, messageEl.firstChild);

            document.body.appendChild(messageEl);

            // 自动移除
            setTimeout(() => {
                messageEl.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.parentNode.removeChild(messageEl);
                    }
                }, 300);
            }, duration);
        }

        // 兼容性函数
        function showSuccessMessage(message) {
            showMessage(message, 'success');
        }

        function showErrorMessage(message) {
            showMessage(message, 'error');
        }

        function showWarningMessage(message) {
            showMessage(message, 'warning');
        }

        function showInfoMessage(message) {
            showMessage(message, 'info');
        }

        // 🎨 主题系统函数
        function showThemeSelector() {
            const panel = document.getElementById('themePanel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';

                // 高亮当前主题
                const currentTheme = localStorage.getItem('currentTheme') || 'default';
                document.querySelectorAll('.theme-option').forEach(option => {
                    option.classList.remove('active');
                    if (option.dataset.theme === currentTheme) {
                        option.classList.add('active');
                    }
                });
            }
        }

        function hideThemeSelector() {
            const panel = document.getElementById('themePanel');
            if (panel) {
                panel.style.display = 'none';
            }
        }

        function applyTheme(themeName) {
            // 移除所有主题类
            document.body.classList.remove('theme-sunset', 'theme-forest', 'theme-ocean', 'theme-purple', 'theme-rose');

            // 应用新主题
            if (themeName !== 'default') {
                document.body.classList.add(`theme-${themeName}`);
            }

            // 保存主题设置
            localStorage.setItem('currentTheme', themeName);

            // 更新主题选择器状态
            document.querySelectorAll('.theme-option').forEach(option => {
                option.classList.remove('active');
                if (option.dataset.theme === themeName) {
                    option.classList.add('active');
                }
            });

            // 显示成功消息
            const themeNames = {
                'default': '🌟 默认主题',
                'sunset': '🌅 日落主题',
                'forest': '🌲 森林主题',
                'ocean': '🌊 海洋主题',
                'purple': '💜 紫罗兰主题',
                'rose': '🌹 玫瑰主题'
            };
            showSuccessMessage(`已切换到 ${themeNames[themeName]}`);

            // 自动关闭主题选择器
            setTimeout(() => {
                hideThemeSelector();
            }, 1000);
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('currentTheme');
            if (savedTheme && savedTheme !== 'default') {
                applyTheme(savedTheme);
            }
        }

        // 在页面加载时初始化主题
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
            initSearchHistory();
            initAccessibility();
            loadAutoBackupSettings();
            updateMainStorageStats();
        });

        // 🎯 可访问性增强功能

        let currentFocusIndex = -1;
        let focusableElements = [];

        function initAccessibility() {
            // 初始化键盘导航
            initKeyboardNavigation();

            // 添加高对比度模式支持
            initHighContrastMode();

            // 添加屏幕阅读器支持
            initScreenReaderSupport();

            // 添加焦点管理
            initFocusManagement();
        }

        // 键盘导航
        function initKeyboardNavigation() {
            document.addEventListener('keydown', function(e) {
                // 如果在输入框中，不处理导航键
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
                    return;
                }

                switch(e.key) {
                    case 'Tab':
                        if (!e.shiftKey) {
                            e.preventDefault();
                            navigateToNext();
                        } else {
                            e.preventDefault();
                            navigateToPrevious();
                        }
                        break;
                    case 'Enter':
                    case ' ':
                        e.preventDefault();
                        activateCurrentElement();
                        break;
                    case 'Escape':
                        e.preventDefault();
                        closeAllPanels();
                        break;
                    case 'ArrowDown':
                        e.preventDefault();
                        navigateToNext();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        navigateToPrevious();
                        break;
                    case 'Home':
                        e.preventDefault();
                        navigateToFirst();
                        break;
                    case 'End':
                        e.preventDefault();
                        navigateToLast();
                        break;
                    case 'f':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            focusSearchInput();
                        }
                        break;
                    case 'n':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            focusTextarea();
                        }
                        break;
                }
            });
        }

        // 更新可聚焦元素列表
        function updateFocusableElements() {
            focusableElements = Array.from(document.querySelectorAll(`
                .snippet,
                button:not([disabled]),
                input:not([disabled]),
                textarea:not([disabled]),
                select:not([disabled]),
                [tabindex]:not([tabindex="-1"])
            `)).filter(el => {
                return el.offsetParent !== null && // 元素可见
                       !el.closest('[style*="display: none"]'); // 父元素未隐藏
            });
        }

        // 导航到下一个元素
        function navigateToNext() {
            updateFocusableElements();
            if (focusableElements.length === 0) return;

            currentFocusIndex = (currentFocusIndex + 1) % focusableElements.length;
            focusElement(focusableElements[currentFocusIndex]);
        }

        // 导航到上一个元素
        function navigateToPrevious() {
            updateFocusableElements();
            if (focusableElements.length === 0) return;

            currentFocusIndex = currentFocusIndex <= 0 ? focusableElements.length - 1 : currentFocusIndex - 1;
            focusElement(focusableElements[currentFocusIndex]);
        }

        // 导航到第一个元素
        function navigateToFirst() {
            updateFocusableElements();
            if (focusableElements.length === 0) return;

            currentFocusIndex = 0;
            focusElement(focusableElements[currentFocusIndex]);
        }

        // 导航到最后一个元素
        function navigateToLast() {
            updateFocusableElements();
            if (focusableElements.length === 0) return;

            currentFocusIndex = focusableElements.length - 1;
            focusElement(focusableElements[currentFocusIndex]);
        }

        // 聚焦元素
        function focusElement(element) {
            if (!element) return;

            element.focus();
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 添加视觉焦点指示器
            element.style.outline = '3px solid var(--primary-blue)';
            element.style.outlineOffset = '2px';

            // 清除其他元素的焦点指示器
            focusableElements.forEach(el => {
                if (el !== element) {
                    el.style.outline = '';
                    el.style.outlineOffset = '';
                }
            });
        }

        // 激活当前元素
        function activateCurrentElement() {
            const currentElement = focusableElements[currentFocusIndex];
            if (!currentElement) return;

            if (currentElement.tagName === 'BUTTON') {
                currentElement.click();
            } else if (currentElement.classList.contains('snippet')) {
                // 激活snippet的复制功能
                const index = parseInt(currentElement.dataset.index);
                copySnippet(index);
            }
        }

        // 关闭所有面板
        function closeAllPanels() {
            const panels = [
                'quickTagPanel',
                'searchSuggestionsPanel',
                'storageManagerPanel'
            ];

            panels.forEach(panelId => {
                const panel = document.getElementById(panelId);
                if (panel) {
                    panel.style.display = 'none';
                }
            });

            // 聚焦到搜索框
            focusSearchInput();
        }

        // 聚焦搜索框
        function focusSearchInput() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // 聚焦文本区域
        function focusTextarea() {
            const textarea = document.querySelector('textarea');
            if (textarea) {
                textarea.focus();
            }
        }

        // 高对比度模式
        function initHighContrastMode() {
            // 检查系统偏好
            if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
                enableHighContrastMode();
            }

            // 添加切换按钮
            addHighContrastToggle();
        }

        function enableHighContrastMode() {
            document.body.classList.add('high-contrast');
            localStorage.setItem('highContrastMode', 'true');
        }

        function disableHighContrastMode() {
            document.body.classList.remove('high-contrast');
            localStorage.setItem('highContrastMode', 'false');
        }

        function toggleHighContrastMode() {
            if (document.body.classList.contains('high-contrast')) {
                disableHighContrastMode();
                showSuccessMessage('🎨 已关闭高对比度模式');
            } else {
                enableHighContrastMode();
                showSuccessMessage('🎨 已开启高对比度模式');
            }
        }

        function addHighContrastToggle() {
            const header = document.querySelector('.header');
            if (header) {
                const toggleBtn = document.createElement('button');
                toggleBtn.innerHTML = '🎨';
                toggleBtn.title = '切换高对比度模式 (Alt+C)';
                toggleBtn.className = 'dark-mode-toggle';
                toggleBtn.style.marginLeft = '10px';
                toggleBtn.onclick = toggleHighContrastMode;

                // 添加键盘快捷键
                document.addEventListener('keydown', function(e) {
                    if (e.altKey && e.key === 'c') {
                        e.preventDefault();
                        toggleHighContrastMode();
                    }
                });

                header.appendChild(toggleBtn);
            }

            // 加载保存的设置
            if (localStorage.getItem('highContrastMode') === 'true') {
                enableHighContrastMode();
            }
        }

        // 屏幕阅读器支持
        function initScreenReaderSupport() {
            // 添加ARIA标签
            addAriaLabels();

            // 添加实时区域用于状态更新
            addLiveRegion();

            // 添加跳转链接
            addSkipLinks();
        }

        function addAriaLabels() {
            // 为主要区域添加标签
            const snippetsDiv = document.getElementById('snippets');
            if (snippetsDiv) {
                snippetsDiv.setAttribute('role', 'main');
                snippetsDiv.setAttribute('aria-label', '剪切板内容列表');
            }

            // 为搜索框添加标签
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.setAttribute('aria-label', '搜索剪切板内容');
                searchInput.setAttribute('aria-describedby', 'search-help');
            }

            // 为文本区域添加标签
            const textarea = document.querySelector('textarea');
            if (textarea) {
                textarea.setAttribute('aria-label', '输入新的剪切板内容');
            }
        }

        function addLiveRegion() {
            const liveRegion = document.createElement('div');
            liveRegion.id = 'live-region';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.style.cssText = 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;';
            document.body.appendChild(liveRegion);
        }

        function announceToScreenReader(message) {
            const liveRegion = document.getElementById('live-region');
            if (liveRegion) {
                liveRegion.textContent = message;
                setTimeout(() => {
                    liveRegion.textContent = '';
                }, 1000);
            }
        }

        function addSkipLinks() {
            const skipLinks = document.createElement('div');
            skipLinks.className = 'skip-links';
            skipLinks.innerHTML = `
                <a href="#main-content" class="skip-link">跳转到主内容</a>
                <a href="#search-input" class="skip-link">跳转到搜索</a>
                <a href="#add-content" class="skip-link">跳转到添加内容</a>
            `;
            document.body.insertBefore(skipLinks, document.body.firstChild);
        }

        // 焦点管理
        function initFocusManagement() {
            // 为snippet添加焦点支持
            document.addEventListener('click', function(e) {
                if (e.target.closest('.snippet')) {
                    const snippet = e.target.closest('.snippet');
                    const index = Array.from(focusableElements).indexOf(snippet);
                    if (index !== -1) {
                        currentFocusIndex = index;
                    }
                }
            });

            // 监听焦点变化
            document.addEventListener('focusin', function(e) {
                const index = Array.from(focusableElements).indexOf(e.target);
                if (index !== -1) {
                    currentFocusIndex = index;
                }
            });
        }

        // 🚀 快速操作功能

        // 快速复制全部内容
        function quickCopyAll() {
            const snippets = getSnippets();
            if (snippets.length === 0) {
                showWarningMessage('没有内容可复制');
                return;
            }

            const allContent = snippets.map(snippet => {
                const textContent = snippet.content.replace(/<[^>]+>/g, '');
                const tags = snippet.tags && snippet.tags.length > 0 ? ` [${snippet.tags.join(', ')}]` : '';
                const section = snippet.section && snippet.section !== '默认' ? ` (${snippet.section})` : '';
                return `${textContent}${tags}${section}`;
            }).join('\n\n');

            navigator.clipboard.writeText(allContent)
                .then(() => showSuccessMessage(`📋 已复制 ${snippets.length} 条内容到剪贴板`))
                .catch(() => showErrorMessage('复制失败'));
        }

        // 快速复制当前可见内容
        function quickCopyVisible() {
            const visibleSnippets = document.querySelectorAll('.snippet:not([style*="display: none"])');
            if (visibleSnippets.length === 0) {
                showWarningMessage('没有可见内容可复制');
                return;
            }

            const visibleContent = Array.from(visibleSnippets).map(element => {
                const contentElement = element.querySelector('.snippet-content');
                return contentElement ? contentElement.textContent.trim() : '';
            }).filter(content => content).join('\n\n');

            navigator.clipboard.writeText(visibleContent)
                .then(() => showSuccessMessage(`👁️ 已复制 ${visibleSnippets.length} 条可见内容`))
                .catch(() => showErrorMessage('复制失败'));
        }

        // 显示快速标签管理器
        function showQuickTagManager() {
            const panel = document.getElementById('quickTagPanel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
                if (panel.style.display === 'block') {
                    updateTagStatistics();
                    displayAllTags();
                }
            }
        }

        function hideQuickTagManager() {
            const panel = document.getElementById('quickTagPanel');
            if (panel) {
                panel.style.display = 'none';
            }
        }

        // 更新标签统计
        function updateTagStatistics() {
            const snippets = getSnippets();
            const allTags = new Set();
            const usedTags = new Set();

            snippets.forEach(snippet => {
                if (snippet.tags && snippet.tags.length > 0) {
                    snippet.tags.forEach(tag => {
                        allTags.add(tag);
                        usedTags.add(tag);
                    });
                }
            });

            // 从全局标签集合中获取所有标签
            const globalTags = Array.from(window.allTags || new Set());
            globalTags.forEach(tag => allTags.add(tag));

            const totalTags = allTags.size;
            const usedTagsCount = usedTags.size;
            const unusedTagsCount = totalTags - usedTagsCount;

            document.getElementById('totalTagsCount').textContent = totalTags;
            document.getElementById('usedTagsCount').textContent = usedTagsCount;
            document.getElementById('unusedTagsCount').textContent = unusedTagsCount;
        }

        // 显示所有标签
        function displayAllTags() {
            const container = document.getElementById('allTagsList');
            if (!container) return;

            const snippets = getSnippets();
            const tagUsage = new Map();

            // 统计标签使用次数
            snippets.forEach(snippet => {
                if (snippet.tags && snippet.tags.length > 0) {
                    snippet.tags.forEach(tag => {
                        tagUsage.set(tag, (tagUsage.get(tag) || 0) + 1);
                    });
                }
            });

            // 获取所有标签（包括未使用的）
            const allTags = Array.from(window.allTags || new Set());
            allTags.forEach(tag => {
                if (!tagUsage.has(tag)) {
                    tagUsage.set(tag, 0);
                }
            });

            container.innerHTML = '';

            // 按使用次数排序
            const sortedTags = Array.from(tagUsage.entries()).sort((a, b) => b[1] - a[1]);

            sortedTags.forEach(([tag, count]) => {
                const tagElement = document.createElement('span');
                tagElement.style.cssText = `
                    display: inline-flex;
                    align-items: center;
                    gap: 4px;
                    padding: 4px 8px;
                    background: ${count > 0 ? 'var(--primary-blue)' : 'var(--gray-400)'};
                    color: white;
                    border-radius: 12px;
                    font-size: 11px;
                    cursor: pointer;
                    transition: var(--transition);
                `;

                tagElement.innerHTML = `
                    <span>${tag}</span>
                    <span style="background: rgba(255,255,255,0.3); border-radius: 8px; padding: 1px 4px; font-size: 9px;">${count}</span>
                    <span onclick="removeTag('${tag}')" style="margin-left: 2px; cursor: pointer; opacity: 0.7; hover: opacity: 1;">×</span>
                `;

                tagElement.addEventListener('click', (e) => {
                    if (e.target.textContent !== '×') {
                        searchByTag(tag);
                    }
                });

                container.appendChild(tagElement);
            });
        }

        // 按标签搜索
        function searchByTag(tag) {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = tag;
                currentSearchTerm = tag.toLowerCase();
                loadSnippets();
                hideQuickTagManager();
                showSuccessMessage(`🔍 搜索标签: ${tag}`);
            }
        }

        // 移除标签
        function removeTag(tag) {
            if (confirm(`确定要删除标签 "${tag}" 吗？这将从所有内容中移除此标签。`)) {
                const snippets = getSnippets();
                let removedCount = 0;

                snippets.forEach(snippet => {
                    if (snippet.tags && snippet.tags.includes(tag)) {
                        snippet.tags = snippet.tags.filter(t => t !== tag);
                        removedCount++;
                    }
                });

                // 从全局标签集合中移除
                if (window.allTags) {
                    window.allTags.delete(tag);
                }

                if (saveSnippets(snippets)) {
                    showSuccessMessage(`🗑️ 已删除标签 "${tag}"，影响 ${removedCount} 条内容`);
                    updateTagStatistics();
                    displayAllTags();
                    loadSnippets();
                }
            }
        }

        // 清理未使用的标签
        function cleanUnusedTags() {
            const snippets = getSnippets();
            const usedTags = new Set();

            snippets.forEach(snippet => {
                if (snippet.tags && snippet.tags.length > 0) {
                    snippet.tags.forEach(tag => usedTags.add(tag));
                }
            });

            const allTags = Array.from(window.allTags || new Set());
            const unusedTags = allTags.filter(tag => !usedTags.has(tag));

            if (unusedTags.length === 0) {
                showInfoMessage('没有未使用的标签需要清理');
                return;
            }

            if (confirm(`发现 ${unusedTags.length} 个未使用的标签，确定要清理吗？\n${unusedTags.join(', ')}`)) {
                unusedTags.forEach(tag => {
                    if (window.allTags) {
                        window.allTags.delete(tag);
                    }
                });

                showSuccessMessage(`🧹 已清理 ${unusedTags.length} 个未使用的标签`);
                updateTagStatistics();
                displayAllTags();
            }
        }

        // 导出标签
        function exportTags() {
            const allTags = Array.from(window.allTags || new Set());
            if (allTags.length === 0) {
                showWarningMessage('没有标签可导出');
                return;
            }

            const tagData = {
                exportTime: new Date().toISOString(),
                totalTags: allTags.length,
                tags: allTags.sort()
            };

            const dataStr = JSON.stringify(tagData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `tags_export_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            URL.revokeObjectURL(url);
            showSuccessMessage(`📤 已导出 ${allTags.length} 个标签`);
        }

        // 🔍 搜索建议功能

        let searchHistory = [];
        const MAX_SEARCH_HISTORY = 10;

        // 初始化搜索历史
        function initSearchHistory() {
            const saved = localStorage.getItem('searchHistory');
            if (saved) {
                try {
                    searchHistory = JSON.parse(saved);
                } catch (e) {
                    searchHistory = [];
                }
            }
        }

        // 保存搜索历史
        function saveSearchHistory() {
            localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
        }

        // 添加搜索记录
        function addSearchRecord(query) {
            if (!query || query.trim().length < 2) return;

            const trimmedQuery = query.trim();

            // 移除重复项
            searchHistory = searchHistory.filter(item => item !== trimmedQuery);

            // 添加到开头
            searchHistory.unshift(trimmedQuery);

            // 限制数量
            if (searchHistory.length > MAX_SEARCH_HISTORY) {
                searchHistory = searchHistory.slice(0, MAX_SEARCH_HISTORY);
            }

            saveSearchHistory();
        }

        // 显示搜索建议
        function showSearchSuggestions() {
            const panel = document.getElementById('searchSuggestionsPanel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
                if (panel.style.display === 'block') {
                    updateSearchSuggestions();
                }
            }
        }

        function hideSearchSuggestions() {
            const panel = document.getElementById('searchSuggestionsPanel');
            if (panel) {
                panel.style.display = 'none';
            }
        }

        // 更新搜索建议
        function updateSearchSuggestions() {
            updatePopularSearches();
            updateRecentSearches();
            updateSuggestedSearches();
        }

        // 更新热门搜索
        function updatePopularSearches() {
            const container = document.getElementById('popularSearches');
            if (!container) return;

            const snippets = getSnippets();
            const wordFreq = new Map();

            // 统计词频
            snippets.forEach(snippet => {
                const text = snippet.content.replace(/<[^>]+>/g, '').toLowerCase();
                const words = text.split(/\s+/).filter(word => word.length > 2);
                words.forEach(word => {
                    wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
                });

                // 统计标签频率
                if (snippet.tags) {
                    snippet.tags.forEach(tag => {
                        wordFreq.set(tag.toLowerCase(), (wordFreq.get(tag.toLowerCase()) || 0) + 2); // 标签权重更高
                    });
                }
            });

            // 获取前8个热门词汇
            const popular = Array.from(wordFreq.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 8)
                .map(([word]) => word);

            container.innerHTML = '';
            popular.forEach(word => {
                const tag = createSearchTag(word, '🔥');
                container.appendChild(tag);
            });
        }

        // 更新最近搜索
        function updateRecentSearches() {
            const container = document.getElementById('recentSearches');
            if (!container) return;

            container.innerHTML = '';
            searchHistory.slice(0, 6).forEach(query => {
                const tag = createSearchTag(query, '🕒');
                container.appendChild(tag);
            });
        }

        // 更新推荐搜索
        function updateSuggestedSearches() {
            const container = document.getElementById('suggestedSearches');
            if (!container) return;

            const suggestions = [
                '今天', '昨天', '本周', '本月',
                '代码', '链接', '笔记', '待办',
                '重要', '紧急', '完成', '进行中'
            ];

            container.innerHTML = '';
            suggestions.forEach(suggestion => {
                const tag = createSearchTag(suggestion, '💡');
                container.appendChild(tag);
            });
        }

        // 创建搜索标签元素
        function createSearchTag(text, icon) {
            const tag = document.createElement('span');
            tag.style.cssText = `
                display: inline-flex;
                align-items: center;
                gap: 4px;
                padding: 4px 8px;
                background: var(--primary-blue);
                color: white;
                border-radius: 12px;
                font-size: 11px;
                cursor: pointer;
                transition: var(--transition);
            `;

            tag.innerHTML = `${icon} ${text}`;

            tag.addEventListener('click', () => {
                performSearch(text);
                hideSearchSuggestions();
            });

            tag.addEventListener('mouseenter', () => {
                tag.style.background = 'var(--primary-blue-dark)';
                tag.style.transform = 'translateY(-1px)';
            });

            tag.addEventListener('mouseleave', () => {
                tag.style.background = 'var(--primary-blue)';
                tag.style.transform = 'translateY(0)';
            });

            return tag;
        }

        // 执行搜索
        function performSearch(query) {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = query;
                currentSearchTerm = query.toLowerCase();
                addSearchRecord(query);
                loadSnippets();
                showSuccessMessage(`🔍 搜索: ${query}`);
            }
        }

        // 从搜索按钮执行搜索
        function performSearchFromButton() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                const query = searchInput.value.trim();
                if (query) {
                    currentSearchTerm = query.toLowerCase();
                    addSearchRecord(query);
                    loadSnippets();
                    showSuccessMessage(`🔍 搜索: ${query}`);
                } else {
                    // 如果搜索框为空，显示所有内容
                    currentSearchTerm = '';
                    loadSnippets();
                    showSuccessMessage('🔍 显示所有内容');
                }
            }
        }

        // 清空搜索历史
        function clearSearchHistory() {
            if (confirm('确定要清空搜索历史吗？')) {
                searchHistory = [];
                saveSearchHistory();
                updateRecentSearches();
                showSuccessMessage('🗑️ 搜索历史已清空');
            }
        }

        // 增强原有的搜索函数
        const originalSearchSnippets = window.searchSnippets;
        window.searchSnippets = function() {
            const query = document.getElementById('searchInput').value;
            if (query.trim()) {
                addSearchRecord(query);
            }
            if (originalSearchSnippets) {
                originalSearchSnippets();
            } else {
                currentSearchTerm = query.toLowerCase();
                if (query.trim() === '') {
                    currentPage = 0;
                    loadSnippets();
                } else {
                    debouncedSearch(query);
                }
            }
        };

        // 🗄️ 存储管理功能

        // 显示存储管理器
        function showStorageManager() {
            const panel = document.getElementById('storageManagerPanel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
                if (panel.style.display === 'block') {
                    updateStorageManagerStats();
                    displayStorageItems();
                    loadAutoBackupSettings();
                }
            }
        }

        function hideStorageManager() {
            const panel = document.getElementById('storageManagerPanel');
            if (panel) {
                panel.style.display = 'none';
            }
        }

        // 更新存储管理器统计
        function updateStorageManagerStats() {
            try {
                const used = JSON.stringify(localStorage).length;
                const snippets = getSnippets();
                const backupKeys = Object.keys(localStorage).filter(key => key.startsWith('snippets_backup_'));

                // 计算压缩率
                const originalSize = JSON.stringify(snippets).length;
                const compressedSize = compressData(snippets).length;
                const compressionRatio = originalSize > 0 ? ((originalSize - compressedSize) / originalSize * 100).toFixed(1) : 0;

                document.getElementById('totalStorageUsed').textContent = formatBytes(used);
                document.getElementById('compressionRatio').textContent = compressionRatio + '%';
                document.getElementById('backupCount').textContent = backupKeys.length;
                document.getElementById('dataItems').textContent = snippets.length;

                // 更新主面板的存储统计
                updateMainStorageStats();
            } catch (error) {
                console.error('更新存储统计失败:', error);
            }
        }

        // 更新主面板存储统计
        function updateMainStorageStats() {
            try {
                const used = JSON.stringify(localStorage).length;
                const quota = 5 * 1024 * 1024; // 5MB
                const percentage = (used / quota * 100).toFixed(1);
                const backupTimestamp = localStorage.getItem('backup_timestamp');

                document.getElementById('storageUsed').textContent = `已使用: ${formatBytes(used)}`;

                const snippets = getSnippets();
                const originalSize = JSON.stringify(snippets).length;
                const compressedSize = compressData(snippets).length;
                const compressionRatio = originalSize > 0 ? ((originalSize - compressedSize) / originalSize * 100).toFixed(1) : 0;
                document.getElementById('storageCompression').textContent = `压缩率: ${compressionRatio}%`;

                document.getElementById('lastBackup').textContent = backupTimestamp ?
                    `最后备份: ${new Date(backupTimestamp).toLocaleString()}` : '最后备份: 无';

                const storageBar = document.getElementById('storageBar');
                if (storageBar) {
                    storageBar.style.width = Math.min(percentage, 100) + '%';
                    storageBar.style.background = percentage > 80 ? 'var(--danger)' :
                                                 percentage > 60 ? 'var(--warning)' : 'var(--primary-blue)';
                }
            } catch (error) {
                console.error('更新主存储统计失败:', error);
            }
        }

        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 显示存储项目
        function displayStorageItems() {
            const container = document.getElementById('storageItemsList');
            if (!container) return;

            container.innerHTML = '';

            // 获取所有localStorage项目
            const items = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                const size = value ? value.length : 0;
                items.push({ key, size, value });
            }

            // 按大小排序
            items.sort((a, b) => b.size - a.size);

            items.forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 5px 0; border-bottom: 1px solid var(--card-border);';

                const isBackup = item.key.startsWith('snippets_backup_');
                const isMain = item.key === 'snippets';

                itemDiv.innerHTML = `
                    <div style="flex: 1;">
                        <span style="font-weight: ${isMain ? 'bold' : 'normal'}; color: ${isMain ? 'var(--primary-blue)' : isBackup ? 'var(--warning)' : 'var(--text-color)'};">
                            ${item.key}
                        </span>
                        ${isMain ? ' (主数据)' : isBackup ? ' (备份)' : ''}
                    </div>
                    <div style="font-size: 11px; color: var(--text-medium);">
                        ${formatBytes(item.size)}
                    </div>
                    ${!isMain ? `<button onclick="removeStorageItem('${item.key}')" style="margin-left: 8px; padding: 2px 6px; background: var(--button-danger); color: white; border: none; border-radius: 3px; font-size: 10px; cursor: pointer;">删除</button>` : ''}
                `;

                container.appendChild(itemDiv);
            });
        }

        // 移除存储项目
        function removeStorageItem(key) {
            if (confirm(`确定要删除存储项目 "${key}" 吗？`)) {
                localStorage.removeItem(key);
                showSuccessMessage(`🗑️ 已删除存储项目: ${key}`);
                updateStorageManagerStats();
                displayStorageItems();
            }
        }

        // 优化存储
        function optimizeStorage() {
            try {
                const snippets = getSnippets();
                const originalSize = JSON.stringify(localStorage).length;

                // 重新压缩主数据
                saveSnippets(snippets);

                // 清理重复的备份
                const backupKeys = Object.keys(localStorage).filter(key => key.startsWith('snippets_backup_'));
                const backups = new Map();

                backupKeys.forEach(key => {
                    const data = localStorage.getItem(key);
                    const hash = btoa(data).slice(0, 10); // 简单哈希
                    if (backups.has(hash)) {
                        localStorage.removeItem(key);
                    } else {
                        backups.set(hash, key);
                    }
                });

                const newSize = JSON.stringify(localStorage).length;
                const saved = originalSize - newSize;

                showSuccessMessage(`🗜️ 存储优化完成，节省了 ${formatBytes(saved)} 空间`);
                updateStorageManagerStats();
                displayStorageItems();
            } catch (error) {
                showErrorMessage('存储优化失败: ' + error.message);
            }
        }

        // 创建手动备份
        function createManualBackup() {
            try {
                const snippets = getSnippets();
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const backupKey = `snippets_backup_manual_${timestamp}`;

                localStorage.setItem(backupKey, JSON.stringify(snippets));
                showSuccessMessage(`💾 手动备份已创建: ${backupKey}`);
                updateStorageManagerStats();
                displayStorageItems();
            } catch (error) {
                showErrorMessage('创建备份失败: ' + error.message);
            }
        }

        // 清理旧备份
        function cleanOldBackups() {
            try {
                const backupKeys = Object.keys(localStorage).filter(key => key.startsWith('snippets_backup_'));

                if (backupKeys.length <= 3) {
                    showInfoMessage('备份数量较少，无需清理');
                    return;
                }

                // 保留最新的3个备份
                backupKeys.sort().slice(0, -3).forEach(key => {
                    localStorage.removeItem(key);
                });

                const cleaned = backupKeys.length - 3;
                showSuccessMessage(`🧹 已清理 ${cleaned} 个旧备份`);
                updateStorageManagerStats();
                displayStorageItems();
            } catch (error) {
                showErrorMessage('清理备份失败: ' + error.message);
            }
        }

        // 重置存储
        function resetStorage() {
            if (confirm('⚠️ 警告：这将清除所有数据和备份，确定要继续吗？\n\n建议先导出数据进行备份。')) {
                if (confirm('最后确认：真的要重置所有存储吗？此操作不可恢复！')) {
                    try {
                        // 清除所有相关数据
                        const keysToRemove = Object.keys(localStorage).filter(key =>
                            key.startsWith('snippets') ||
                            key.startsWith('sections') ||
                            key.startsWith('searchHistory') ||
                            key.startsWith('storage_') ||
                            key.startsWith('backup_')
                        );

                        keysToRemove.forEach(key => localStorage.removeItem(key));

                        // 重新初始化
                        allSections = new Set(['默认']);
                        allTags = new Set();
                        searchHistory = [];

                        showSuccessMessage('🔄 存储已重置，页面将刷新');
                        setTimeout(() => location.reload(), 1500);
                    } catch (error) {
                        showErrorMessage('重置存储失败: ' + error.message);
                    }
                }
            }
        }

        // 自动备份功能
        let autoBackupInterval = null;

        function toggleAutoBackup() {
            const enabled = document.getElementById('autoBackupEnabled').checked;
            const interval = parseInt(document.getElementById('backupInterval').value);

            if (enabled) {
                startAutoBackup(interval);
                localStorage.setItem('autoBackupEnabled', 'true');
                localStorage.setItem('autoBackupInterval', interval.toString());
                showSuccessMessage(`✅ 自动备份已启用，间隔 ${interval} 分钟`);
            } else {
                stopAutoBackup();
                localStorage.setItem('autoBackupEnabled', 'false');
                showInfoMessage('⏸️ 自动备份已停用');
            }
        }

        function startAutoBackup(intervalMinutes) {
            stopAutoBackup(); // 清除现有定时器

            autoBackupInterval = setInterval(() => {
                try {
                    createAutoBackup();
                    console.log('自动备份完成');
                } catch (error) {
                    console.error('自动备份失败:', error);
                }
            }, intervalMinutes * 60 * 1000);
        }

        function stopAutoBackup() {
            if (autoBackupInterval) {
                clearInterval(autoBackupInterval);
                autoBackupInterval = null;
            }
        }

        function loadAutoBackupSettings() {
            const enabled = localStorage.getItem('autoBackupEnabled') === 'true';
            const interval = parseInt(localStorage.getItem('autoBackupInterval')) || 15;

            document.getElementById('autoBackupEnabled').checked = enabled;
            document.getElementById('backupInterval').value = interval;

            if (enabled) {
                startAutoBackup(interval);
            }
        }

        // 📋 多格式导出功能

        function showExportOptions() {
            const panel = document.getElementById('exportOptionsPanel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        }

        function hideExportOptions() {
            const panel = document.getElementById('exportOptionsPanel');
            if (panel) {
                panel.style.display = 'none';
            }
        }

        // 获取导出数据
        function getExportData() {
            const includeTimestamp = document.getElementById('includeTimestamp').checked;
            const includeTags = document.getElementById('includeTags').checked;
            const includeSection = document.getElementById('includeSection').checked;
            const onlyVisible = document.getElementById('onlyVisible').checked;

            let snippets;
            if (onlyVisible) {
                // 获取当前可见的snippets
                const visibleElements = document.querySelectorAll('.snippet:not([style*="display: none"])');
                const allSnippets = getSnippets();
                snippets = Array.from(visibleElements).map(el => {
                    const index = parseInt(el.dataset.index);
                    return allSnippets.find(s => s.index === index);
                }).filter(Boolean);
            } else {
                snippets = getSnippets();
            }

            return snippets.map(snippet => ({
                content: snippet.content.replace(/<[^>]+>/g, ''), // 移除HTML标签
                timestamp: includeTimestamp ? snippet.timestamp : undefined,
                tags: includeTags ? snippet.tags : undefined,
                section: includeSection ? snippet.section : undefined,
                pinned: snippet.pinned
            }));
        }

        // 导出为Markdown
        function exportAsMarkdown() {
            try {
                const data = getExportData();
                const timestamp = new Date().toISOString().split('T')[0];

                let markdown = `# 剪切板导出\n\n`;
                markdown += `导出时间: ${new Date().toLocaleString()}\n`;
                markdown += `总计: ${data.length} 条记录\n\n`;

                // 按分区分组
                const sections = {};
                data.forEach(item => {
                    const section = item.section || '默认';
                    if (!sections[section]) {
                        sections[section] = [];
                    }
                    sections[section].push(item);
                });

                Object.keys(sections).forEach(sectionName => {
                    markdown += `## ${sectionName}\n\n`;

                    sections[sectionName].forEach((item, index) => {
                        markdown += `### ${index + 1}. ${item.pinned ? '📌 ' : ''}`;

                        // 添加第一行作为标题
                        const firstLine = item.content.split('\n')[0].substring(0, 50);
                        markdown += `${firstLine}${item.content.length > 50 ? '...' : ''}\n\n`;

                        // 内容
                        markdown += '```\n';
                        markdown += item.content;
                        markdown += '\n```\n\n';

                        // 元数据
                        const metadata = [];
                        if (item.timestamp) {
                            metadata.push(`**时间**: ${new Date(item.timestamp).toLocaleString()}`);
                        }
                        if (item.tags && item.tags.length > 0) {
                            metadata.push(`**标签**: ${item.tags.join(', ')}`);
                        }

                        if (metadata.length > 0) {
                            markdown += metadata.join(' | ') + '\n\n';
                        }

                        markdown += '---\n\n';
                    });
                });

                downloadFile(markdown, `clipboard_export_${timestamp}.md`, 'text/markdown');
                showSuccessMessage('📝 Markdown导出完成');
            } catch (error) {
                showErrorMessage('Markdown导出失败: ' + error.message);
            }
        }

        // 导出为CSV
        function exportAsCSV() {
            try {
                const data = getExportData();
                const timestamp = new Date().toISOString().split('T')[0];

                // CSV头部
                const headers = ['序号', '内容', '分区', '标签', '置顶', '时间'];
                let csv = headers.join(',') + '\n';

                data.forEach((item, index) => {
                    const row = [
                        index + 1,
                        `"${item.content.replace(/"/g, '""')}"`, // 转义双引号
                        `"${item.section || '默认'}"`,
                        `"${item.tags ? item.tags.join('; ') : ''}"`,
                        item.pinned ? '是' : '否',
                        item.timestamp ? `"${new Date(item.timestamp).toLocaleString()}"` : ''
                    ];
                    csv += row.join(',') + '\n';
                });

                // 添加BOM以支持中文
                const csvWithBOM = '\uFEFF' + csv;
                downloadFile(csvWithBOM, `clipboard_export_${timestamp}.csv`, 'text/csv');
                showSuccessMessage('📊 CSV导出完成');
            } catch (error) {
                showErrorMessage('CSV导出失败: ' + error.message);
            }
        }

        // 导出为TXT
        function exportAsTXT() {
            try {
                const data = getExportData();
                const timestamp = new Date().toISOString().split('T')[0];

                let txt = `剪切板导出\n`;
                txt += `导出时间: ${new Date().toLocaleString()}\n`;
                txt += `总计: ${data.length} 条记录\n`;
                txt += `${'='.repeat(50)}\n\n`;

                data.forEach((item, index) => {
                    txt += `[${index + 1}] ${item.pinned ? '[置顶] ' : ''}`;
                    txt += `[${item.section || '默认'}]\n`;
                    txt += `${'-'.repeat(30)}\n`;
                    txt += `${item.content}\n`;

                    if (item.tags && item.tags.length > 0) {
                        txt += `标签: ${item.tags.join(', ')}\n`;
                    }

                    if (item.timestamp) {
                        txt += `时间: ${new Date(item.timestamp).toLocaleString()}\n`;
                    }

                    txt += `\n${'='.repeat(50)}\n\n`;
                });

                downloadFile(txt, `clipboard_export_${timestamp}.txt`, 'text/plain');
                showSuccessMessage('📄 TXT导出完成');
            } catch (error) {
                showErrorMessage('TXT导出失败: ' + error.message);
            }
        }

        // 导出为HTML
        function exportAsHTML() {
            try {
                const data = getExportData();
                const timestamp = new Date().toISOString().split('T')[0];

                let html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>剪切板导出 - ${timestamp}</title>
<style>
    body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; margin: 40px; background: #f5f5f5; }
    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    h1 { color: #333; border-bottom: 3px solid #007aff; padding-bottom: 10px; }
    h2 { color: #555; margin-top: 30px; }
    .item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 6px; background: #fafafa; }
    .item.pinned { border-left: 4px solid #ff6b35; }
    .content { background: white; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
    .meta { font-size: 12px; color: #666; margin-top: 10px; }
    .tags { display: inline-block; background: #007aff; color: white; padding: 2px 6px; border-radius: 3px; margin-right: 5px; font-size: 11px; }
    .section { font-weight: bold; color: #007aff; }
    .timestamp { color: #999; }
</style>
</head>
<body>
<div class="container">
    <h1>📋 剪切板导出</h1>
    <p><strong>导出时间:</strong> ${new Date().toLocaleString()}</p>
    <p><strong>总计:</strong> ${data.length} 条记录</p>
`;

                // 按分区分组
                const sections = {};
                data.forEach(item => {
                    const section = item.section || '默认';
                    if (!sections[section]) {
                        sections[section] = [];
                    }
                    sections[section].push(item);
                });

                Object.keys(sections).forEach(sectionName => {
                    html += `        <h2>📁 ${sectionName}</h2>\n`;

                    sections[sectionName].forEach((item, index) => {
                        html += `        <div class="item${item.pinned ? ' pinned' : ''}">
        <div class="content">${item.content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</div>
        <div class="meta">
            <span class="section">分区: ${item.section || '默认'}</span>`;

                        if (item.tags && item.tags.length > 0) {
                            html += ` | 标签: `;
                            item.tags.forEach(tag => {
                                html += `<span class="tags">${tag}</span>`;
                            });
                        }

                        if (item.timestamp) {
                            html += ` | <span class="timestamp">时间: ${new Date(item.timestamp).toLocaleString()}</span>`;
                        }

                        if (item.pinned) {
                            html += ` | <strong style="color: #ff6b35;">📌 置顶</strong>`;
                        }

                        html += `
        </div>
    </div>
`;
                    });
                });

                html += `    </div>
</body>
</html>`;

                downloadFile(html, `clipboard_export_${timestamp}.html`, 'text/html');
                showSuccessMessage('🌐 HTML导出完成');
            } catch (error) {
                showErrorMessage('HTML导出失败: ' + error.message);
            }
        }

        // 导出为JSON
        function exportAsJSON() {
            try {
                const data = getExportData();
                const timestamp = new Date().toISOString().split('T')[0];

                const exportData = {
                    exportInfo: {
                        timestamp: new Date().toISOString(),
                        version: '1.0',
                        totalItems: data.length,
                        source: '网络剪切板'
                    },
                    data: data
                };

                const json = JSON.stringify(exportData, null, 2);
                downloadFile(json, `clipboard_export_${timestamp}.json`, 'application/json');
                showSuccessMessage('⚙️ JSON导出完成');
            } catch (error) {
                showErrorMessage('JSON导出失败: ' + error.message);
            }
        }

        // 导出为PDF（使用浏览器打印功能）
        function exportAsPDF() {
            try {
                const data = getExportData();

                // 创建一个新窗口用于打印
                const printWindow = window.open('', '_blank');

                let html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>剪切板导出 - PDF</title>
<style>
    @media print {
        body { margin: 0; }
        .no-print { display: none; }
    }
    body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.4; margin: 20px; }
    h1 { color: #333; border-bottom: 2px solid #007aff; padding-bottom: 10px; }
    h2 { color: #555; margin-top: 25px; page-break-after: avoid; }
    .item { margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; page-break-inside: avoid; }
    .item.pinned { border-left: 4px solid #ff6b35; }
    .content { background: #f9f9f9; padding: 8px; border-radius: 3px; white-space: pre-wrap; font-family: monospace; font-size: 12px; }
    .meta { font-size: 10px; color: #666; margin-top: 8px; }
    .tags { background: #007aff; color: white; padding: 1px 4px; border-radius: 2px; margin-right: 3px; font-size: 9px; }
    .print-btn { background: #007aff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 20px 0; }
</style>
</head>
<body>
<div class="no-print">
    <button class="print-btn" onclick="window.print()">🖨️ 打印/保存为PDF</button>
    <button class="print-btn" onclick="window.close()" style="background: #666;">关闭</button>
</div>

<h1>📋 剪切板导出</h1>
<p><strong>导出时间:</strong> ${new Date().toLocaleString()}</p>
<p><strong>总计:</strong> ${data.length} 条记录</p>
`;

                // 按分区分组
                const sections = {};
                data.forEach(item => {
                    const section = item.section || '默认';
                    if (!sections[section]) {
                        sections[section] = [];
                    }
                    sections[section].push(item);
                });

                Object.keys(sections).forEach(sectionName => {
                    html += `    <h2>📁 ${sectionName}</h2>\n`;

                    sections[sectionName].forEach((item, index) => {
                        html += `    <div class="item${item.pinned ? ' pinned' : ''}">
    <div class="content">${item.content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</div>
    <div class="meta">
        分区: ${item.section || '默认'}`;

                        if (item.tags && item.tags.length > 0) {
                            html += ` | 标签: `;
                            item.tags.forEach(tag => {
                                html += `<span class="tags">${tag}</span>`;
                            });
                        }

                        if (item.timestamp) {
                            html += ` | 时间: ${new Date(item.timestamp).toLocaleString()}`;
                        }

                        if (item.pinned) {
                            html += ` | 📌 置顶`;
                        }

                        html += `
    </div>
</div>
`;
                    });
                });

                html += `</body></html>`;

                printWindow.document.write(html);
                printWindow.document.close();

                showSuccessMessage('📑 PDF预览窗口已打开，请使用浏览器的打印功能保存为PDF');
            } catch (error) {
                showErrorMessage('PDF导出失败: ' + error.message);
            }
        }

        // 下载文件的通用函数
        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            URL.revokeObjectURL(url);
        }

        // 🤖 AI智能分类系统
        function showAIPanel() {
            const panel = document.getElementById('aiPanel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        }

        function hideAIPanel() {
            const panel = document.getElementById('aiPanel');
            if (panel) {
                panel.style.display = 'none';
            }
        }

        // 智能内容分类
        function runAutoClassification() {
            const snippets = getSnippets();
            if (snippets.length === 0) {
                showSuccessMessage('没有内容需要分类');
                return;
            }

            showSuccessMessage('🤖 AI正在分析内容...');

            setTimeout(() => {
                let classified = 0;
                const newSections = new Set();

                snippets.forEach(snippet => {
                    const category = classifyContent(snippet.content);
                    if (category && category !== '默认' && (!snippet.section || snippet.section === '默认')) {
                        snippet.section = category;
                        newSections.add(category);
                        classified++;
                    }
                });

                if (classified > 0) {
                    // 添加新分区到全局分区列表
                    newSections.forEach(section => allSections.add(section));
                    saveSections();

                    if (saveSnippets(snippets)) {
                        showAIResults(`🎯 智能分类完成！<br>
                            ✅ 已分类 ${classified} 个项目<br>
                            📂 新增分区: ${[...newSections].join(', ')}`);
                        loadSnippets();
                        updateSectionTabs();
                        updateSectionSelects();
                    }
                } else {
                    showAIResults('📝 所有内容都已经有合适的分区了！');
                }
            }, 1500);
        }

        // 内容分类算法
        function classifyContent(content) {
            const text = content.toLowerCase();

            // 代码相关
            if (text.includes('function') || text.includes('const') || text.includes('var') ||
                text.includes('class') || text.includes('import') || text.includes('export') ||
                text.includes('console.log') || text.includes('return') || text.includes('if(') ||
                text.includes('for(') || text.includes('while(') || text.includes('{}') ||
                text.includes('[]') || text.includes('()') || text.includes(';')) {
                return '代码片段';
            }

            // 链接相关
            if (text.includes('http') || text.includes('www.') || text.includes('.com') ||
                text.includes('.org') || text.includes('.net') || text.includes('://')) {
                return '链接收藏';
            }

            // 待办事项
            if (text.includes('todo') || text.includes('待办') || text.includes('任务') ||
                text.includes('完成') || text.includes('deadline') || text.includes('截止') ||
                text.includes('提醒') || text.includes('记住') || text.includes('别忘了')) {
                return '待办事项';
            }

            // 学习笔记
            if (text.includes('学习') || text.includes('笔记') || text.includes('总结') ||
                text.includes('知识') || text.includes('概念') || text.includes('定义') ||
                text.includes('原理') || text.includes('方法') || text.includes('技巧')) {
                return '学习笔记';
            }

            // 工作相关
            if (text.includes('会议') || text.includes('项目') || text.includes('客户') ||
                text.includes('报告') || text.includes('文档') || text.includes('需求') ||
                text.includes('bug') || text.includes('测试') || text.includes('发布')) {
                return '工作相关';
            }

            // 个人信息
            if (text.includes('密码') || text.includes('账号') || text.includes('邮箱') ||
                text.includes('电话') || text.includes('地址') || text.includes('身份证') ||
                text.includes('银行') || text.includes('卡号')) {
                return '个人信息';
            }

            // 购物清单
            if (text.includes('购买') || text.includes('买') || text.includes('价格') ||
                text.includes('元') || text.includes('￥') || text.includes('$') ||
                text.includes('商品') || text.includes('淘宝') || text.includes('京东')) {
                return '购物清单';
            }

            return null; // 无法分类
        }

        // 重复内容检测
        function findDuplicates() {
            const snippets = getSnippets();
            if (snippets.length < 2) {
                showAIResults('📝 内容太少，无法检测重复');
                return;
            }

            showSuccessMessage('🔍 正在检测重复内容...');

            setTimeout(() => {
                const duplicates = [];
                const seen = new Map();

                snippets.forEach((snippet, index) => {
                    const cleanContent = snippet.content.replace(/<[^>]*>/g, '').trim().toLowerCase();
                    const words = cleanContent.split(/\s+/).filter(w => w.length > 2);
                    const fingerprint = words.slice(0, 10).join(' '); // 取前10个词作为指纹

                    if (fingerprint.length > 10) { // 只检测有意义的内容
                        if (seen.has(fingerprint)) {
                            duplicates.push({
                                original: seen.get(fingerprint),
                                duplicate: snippet,
                                similarity: calculateSimilarity(seen.get(fingerprint).content, snippet.content)
                            });
                        } else {
                            seen.set(fingerprint, snippet);
                        }
                    }
                });

                if (duplicates.length > 0) {
                    let resultHtml = `🔍 发现 ${duplicates.length} 组重复内容：<br><br>`;
                    duplicates.forEach((dup, index) => {
                        resultHtml += `<div style="margin-bottom: 10px; padding: 10px; background: rgba(239, 68, 68, 0.1); border-radius: 6px;">
                            <strong>重复组 ${index + 1}</strong> (相似度: ${Math.round(dup.similarity * 100)}%)<br>
                            <small>原始: ${dup.original.content.substring(0, 50)}...</small><br>
                            <small>重复: ${dup.duplicate.content.substring(0, 50)}...</small>
                        </div>`;
                    });
                    showAIResults(resultHtml);
                } else {
                    showAIResults('✅ 没有发现重复内容！');
                }
            }, 1000);
        }

        // 计算文本相似度
        function calculateSimilarity(text1, text2) {
            const clean1 = text1.replace(/<[^>]*>/g, '').toLowerCase();
            const clean2 = text2.replace(/<[^>]*>/g, '').toLowerCase();

            const words1 = new Set(clean1.split(/\s+/));
            const words2 = new Set(clean2.split(/\s+/));

            const intersection = new Set([...words1].filter(x => words2.has(x)));
            const union = new Set([...words1, ...words2]);

            return intersection.size / union.size;
        }

        // 智能标签推荐
        function suggestTags() {
            const snippets = getSnippets();
            if (snippets.length === 0) {
                showAIResults('📝 没有内容可以分析');
                return;
            }

            showSuccessMessage('✨ AI正在分析内容，推荐标签...');

            setTimeout(() => {
                const tagSuggestions = new Map();
                let processedCount = 0;

                snippets.forEach(snippet => {
                    if (!snippet.tags || snippet.tags.length === 0) {
                        const suggestedTags = generateTagsForContent(snippet.content);
                        if (suggestedTags.length > 0) {
                            tagSuggestions.set(snippet.index, suggestedTags);
                            processedCount++;
                        }
                    }
                });

                if (tagSuggestions.size > 0) {
                    let resultHtml = `✨ 为 ${processedCount} 个项目推荐了标签：<br><br>`;
                    tagSuggestions.forEach((tags, index) => {
                        const snippet = snippets.find(s => s.index === index);
                        resultHtml += `<div style="margin-bottom: 10px; padding: 10px; background: rgba(59, 130, 246, 0.1); border-radius: 6px;">
                            <div style="font-weight: 600; margin-bottom: 5px;">内容: ${snippet.content.substring(0, 30)}...</div>
                            <div>推荐标签: ${tags.map(tag => `<span style="background: rgba(59, 130, 246, 0.2); padding: 2px 6px; border-radius: 4px; margin: 2px; font-size: 12px;">${tag}</span>`).join('')}</div>
                            <button onclick="applyTagSuggestions(${index}, ${JSON.stringify(tags).replace(/"/g, '&quot;')})" style="margin-top: 5px; padding: 4px 8px; background: var(--button-primary); color: white; border: none; border-radius: 4px; font-size: 11px; cursor: pointer;">应用标签</button>
                        </div>`;
                    });
                    showAIResults(resultHtml);
                } else {
                    showAIResults('📝 所有内容都已经有标签了！');
                }
            }, 1500);
        }

        // 为内容生成标签
        function generateTagsForContent(content) {
            const text = content.toLowerCase();
            const tags = [];

            // 技术标签
            if (text.includes('javascript') || text.includes('js')) tags.push('JavaScript');
            if (text.includes('python')) tags.push('Python');
            if (text.includes('html') || text.includes('css')) tags.push('前端');
            if (text.includes('react') || text.includes('vue')) tags.push('框架');
            if (text.includes('api') || text.includes('接口')) tags.push('API');
            if (text.includes('数据库') || text.includes('sql')) tags.push('数据库');

            // 工作标签
            if (text.includes('会议') || text.includes('meeting')) tags.push('会议');
            if (text.includes('项目') || text.includes('project')) tags.push('项目');
            if (text.includes('deadline') || text.includes('截止')) tags.push('紧急');
            if (text.includes('bug') || text.includes('错误')) tags.push('Bug');

            // 学习标签
            if (text.includes('学习') || text.includes('教程')) tags.push('学习');
            if (text.includes('笔记') || text.includes('总结')) tags.push('笔记');
            if (text.includes('文档') || text.includes('资料')) tags.push('文档');

            // 生活标签
            if (text.includes('购买') || text.includes('买')) tags.push('购物');
            if (text.includes('旅行') || text.includes('旅游')) tags.push('旅行');
            if (text.includes('健康') || text.includes('运动')) tags.push('健康');

            // 重要性标签
            if (text.includes('重要') || text.includes('urgent')) tags.push('重要');
            if (text.includes('临时') || text.includes('temp')) tags.push('临时');

            return tags.slice(0, 3); // 最多返回3个标签
        }

        // 应用标签建议
        function applyTagSuggestions(snippetIndex, suggestedTags) {
            let snippets = getSnippets();
            const snippet = snippets.find(s => s.index === snippetIndex);

            if (snippet) {
                snippet.tags = [...(snippet.tags || []), ...suggestedTags];
                // 去重
                snippet.tags = [...new Set(snippet.tags)];

                if (saveSnippets(snippets)) {
                    showSuccessMessage(`✅ 已为内容添加标签: ${suggestedTags.join(', ')}`);
                    loadSnippets();
                }
            }
        }

        // 显示AI分析结果
        function showAIResults(html) {
            const resultsDiv = document.getElementById('aiResults');
            const contentDiv = document.getElementById('aiResultsContent');

            if (resultsDiv && contentDiv) {
                contentDiv.innerHTML = html;
                resultsDiv.style.display = 'block';
            }
        }

        // 生成内容摘要
        function generateSummaries() {
            const snippets = getSnippets();
            const longSnippets = snippets.filter(s => s.content.length > 100);

            if (longSnippets.length === 0) {
                showAIResults('📝 没有足够长的内容需要生成摘要');
                return;
            }

            showSuccessMessage('📝 正在生成内容摘要...');

            setTimeout(() => {
                let resultHtml = `📝 为 ${longSnippets.length} 个长内容生成了摘要：<br><br>`;

                longSnippets.forEach(snippet => {
                    const summary = generateSummary(snippet.content);
                    resultHtml += `<div style="margin-bottom: 15px; padding: 10px; background: rgba(139, 92, 246, 0.1); border-radius: 6px;">
                        <div style="font-weight: 600; margin-bottom: 5px;">原内容 (${snippet.content.length} 字符)</div>
                        <div style="font-size: 12px; color: var(--text-medium); margin-bottom: 8px;">${snippet.content.substring(0, 80)}...</div>
                        <div style="font-weight: 600; margin-bottom: 5px;">📝 摘要:</div>
                        <div style="background: white; padding: 8px; border-radius: 4px; font-size: 13px;">${summary}</div>
                    </div>`;
                });

                showAIResults(resultHtml);
            }, 1000);
        }

        // 生成摘要算法
        function generateSummary(content) {
            const cleanText = content.replace(/<[^>]*>/g, '');
            const sentences = cleanText.split(/[.!?。！？]/).filter(s => s.trim().length > 10);

            if (sentences.length <= 2) {
                return cleanText.substring(0, 100) + (cleanText.length > 100 ? '...' : '');
            }

            // 简单的摘要算法：取前两句和最后一句
            const summary = sentences.slice(0, 2).join('。') + '。';
            return summary.length > 150 ? summary.substring(0, 150) + '...' : summary;
        }

        // 📈 数据可视化系统
        function showDataVisualization() {
            const panel = document.getElementById('dataVisualizationPanel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
                if (panel.style.display === 'block') {
                    generateDataVisualization();
                }
            }
        }

        function hideDataVisualization() {
            const panel = document.getElementById('dataVisualizationPanel');
            if (panel) {
                panel.style.display = 'none';
            }
        }

        function refreshDataVisualization() {
            generateDataVisualization();
            showSuccessMessage('📈 数据已刷新！');
        }

        function generateDataVisualization() {
            const snippets = getSnippets();

            // 基础统计
            updateBasicStats(snippets);

            // 分区分布图
            generateSectionChart(snippets);

            // 时间分布图
            generateTimeChart(snippets);

            // 标签云
            generateTagCloud(snippets);

            // 活跃度热力图
            generateHeatMap(snippets);
        }

        function updateBasicStats(snippets) {
            const totalCount = snippets.length;
            const sectionCount = allSections.size;
            const allTagsSet = new Set();
            let totalLength = 0;

            snippets.forEach(snippet => {
                if (snippet.tags) {
                    snippet.tags.forEach(tag => allTagsSet.add(tag));
                }
                totalLength += snippet.content.length;
            });

            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('sectionCount').textContent = sectionCount;
            document.getElementById('tagCount').textContent = allTagsSet.size;
            document.getElementById('avgLength').textContent = totalCount > 0 ? Math.round(totalLength / totalCount) : 0;
        }

        function generateSectionChart(snippets) {
            const sectionCounts = {};

            // 统计每个分区的数量
            snippets.forEach(snippet => {
                const section = snippet.section || '默认';
                sectionCounts[section] = (sectionCounts[section] || 0) + 1;
            });

            const chartContainer = document.getElementById('sectionChart');
            chartContainer.innerHTML = '';

            const maxCount = Math.max(...Object.values(sectionCounts));
            const colors = ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ef4444', '#06b6d4'];

            Object.entries(sectionCounts).forEach(([section, count], index) => {
                const height = (count / maxCount) * 150;
                const color = colors[index % colors.length];

                const bar = document.createElement('div');
                bar.style.cssText = `
                    width: 40px;
                    height: ${height}px;
                    background: ${color};
                    border-radius: 4px 4px 0 0;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    justify-content: end;
                    align-items: center;
                `;

                const label = document.createElement('div');
                label.style.cssText = `
                    position: absolute;
                    bottom: -25px;
                    font-size: 10px;
                    color: var(--text-medium);
                    transform: rotate(-45deg);
                    white-space: nowrap;
                `;
                label.textContent = section.length > 6 ? section.substring(0, 6) + '...' : section;

                const countLabel = document.createElement('div');
                countLabel.style.cssText = `
                    position: absolute;
                    top: -20px;
                    font-size: 11px;
                    font-weight: bold;
                    color: ${color};
                `;
                countLabel.textContent = count;

                bar.appendChild(label);
                bar.appendChild(countLabel);
                chartContainer.appendChild(bar);
            });
        }

        function generateTimeChart(snippets) {
            const timeData = {};
            const now = new Date();

            // 初始化最近7天的数据
            for (let i = 6; i >= 0; i--) {
                const date = new Date(now);
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                timeData[dateStr] = 0;
            }

            // 统计每天的添加数量
            snippets.forEach(snippet => {
                if (snippet.timestamp) {
                    const date = new Date(snippet.timestamp);
                    const dateStr = date.toISOString().split('T')[0];
                    if (timeData.hasOwnProperty(dateStr)) {
                        timeData[dateStr]++;
                    }
                }
            });

            const chartContainer = document.getElementById('timeChart');
            chartContainer.innerHTML = '';

            const maxCount = Math.max(...Object.values(timeData), 1);

            Object.entries(timeData).forEach(([date, count]) => {
                const height = (count / maxCount) * 150;

                const bar = document.createElement('div');
                bar.style.cssText = `
                    width: 25px;
                    height: ${height}px;
                    background: linear-gradient(to top, #3b82f6, #60a5fa);
                    border-radius: 2px 2px 0 0;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    justify-content: end;
                    align-items: center;
                `;

                const label = document.createElement('div');
                label.style.cssText = `
                    position: absolute;
                    bottom: -25px;
                    font-size: 9px;
                    color: var(--text-medium);
                    transform: rotate(-45deg);
                `;
                label.textContent = new Date(date).getDate();

                if (count > 0) {
                    const countLabel = document.createElement('div');
                    countLabel.style.cssText = `
                        position: absolute;
                        top: -15px;
                        font-size: 10px;
                        font-weight: bold;
                        color: #3b82f6;
                    `;
                    countLabel.textContent = count;
                    bar.appendChild(countLabel);
                }

                bar.appendChild(label);
                chartContainer.appendChild(bar);
            });
        }

        function generateTagCloud(snippets) {
            const tagCounts = {};

            snippets.forEach(snippet => {
                if (snippet.tags) {
                    snippet.tags.forEach(tag => {
                        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                    });
                }
            });

            const cloudContainer = document.getElementById('tagCloud');
            cloudContainer.innerHTML = '';

            if (Object.keys(tagCounts).length === 0) {
                cloudContainer.innerHTML = '<div style="color: var(--text-medium); font-style: italic;">暂无标签数据</div>';
                return;
            }

            const maxCount = Math.max(...Object.values(tagCounts));
            const colors = ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ef4444', '#06b6d4', '#f97316'];

            Object.entries(tagCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 20) // 只显示前20个标签
                .forEach(([tag, count], index) => {
                    const fontSize = 12 + (count / maxCount) * 16;
                    const color = colors[index % colors.length];

                    const tagElement = document.createElement('span');
                    tagElement.style.cssText = `
                        display: inline-block;
                        margin: 3px;
                        padding: 4px 8px;
                        background: ${color}20;
                        color: ${color};
                        border-radius: 12px;
                        font-size: ${fontSize}px;
                        font-weight: 500;
                        border: 1px solid ${color}40;
                    `;
                    tagElement.textContent = `${tag} (${count})`;
                    cloudContainer.appendChild(tagElement);
                });
        }

        function generateHeatMap(snippets) {
            const heatMapContainer = document.getElementById('heatMap');
            heatMapContainer.innerHTML = '';

            const now = new Date();
            const dayData = {};

            // 初始化最近7天
            for (let i = 6; i >= 0; i--) {
                const date = new Date(now);
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                dayData[dateStr] = 0;
            }

            // 统计数据
            snippets.forEach(snippet => {
                if (snippet.timestamp) {
                    const date = new Date(snippet.timestamp);
                    const dateStr = date.toISOString().split('T')[0];
                    if (dayData.hasOwnProperty(dateStr)) {
                        dayData[dateStr]++;
                    }
                }
            });

            const maxCount = Math.max(...Object.values(dayData), 1);
            const days = ['日', '一', '二', '三', '四', '五', '六'];

            Object.entries(dayData).forEach(([date, count]) => {
                const intensity = count / maxCount;
                const dayOfWeek = new Date(date).getDay();

                const cell = document.createElement('div');
                cell.style.cssText = `
                    width: 35px;
                    height: 35px;
                    background: rgba(59, 130, 246, ${0.1 + intensity * 0.8});
                    border-radius: 6px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    color: var(--text-color);
                    border: 1px solid rgba(59, 130, 246, 0.2);
                `;

                cell.innerHTML = `
                    <div style="font-weight: bold;">${days[dayOfWeek]}</div>
                    <div style="font-size: 8px;">${count}</div>
                `;

                heatMapContainer.appendChild(cell);
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initPWA();
        });

        // 📱 PWA 支持系统
        let deferredPrompt = null;

        function initPWA() {
            // 创建动态 manifest
            createManifest();

            // 注册 Service Worker
            if ('serviceWorker' in navigator) {
                registerServiceWorker();
            }

            // 监听安装提示
            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                deferredPrompt = e;
                showInstallButton();
            });

            // 检查是否已安装
            window.addEventListener('appinstalled', () => {
                hideInstallButton();
                showSuccessMessage('📱 应用已成功安装！');
            });
        }

        function createManifest() {
            const manifest = {
                name: "网络剪切板",
                short_name: "剪切板",
                description: "功能强大的网络剪切板，支持AI智能分类、数据可视化、多主题等",
                start_url: "./",
                display: "standalone",
                background_color: "#ffffff",
                theme_color: "#3b82f6",
                icons: [
                    {
                        src: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOTIiIGhlaWdodD0iMTkyIj48cmVjdCB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgZmlsbD0iIzNiODJmNiIgcng9IjI0Ii8+PHRleHQgeD0iOTYiIHk9IjEyMCIgZmlsbD0iI2ZmZmZmZiIgZm9udC1zaXplPSI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+8J+QsjwvdGV4dD48L3N2Zz4=",
                        sizes: "192x192",
                        type: "image/svg+xml"
                    },
                    {
                        src: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIj48cmVjdCB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgZmlsbD0iIzNiODJmNiIgcng9IjY0Ii8+PHRleHQgeD0iMjU2IiB5PSIzMjAiIGZpbGw9IiNmZmZmZmYiIGZvbnQtc2l6ZT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7wn5CyPC90ZXh0Pjwvc3ZnPg==",
                        sizes: "512x512",
                        type: "image/svg+xml"
                    }
                ]
            };

            const manifestBlob = new Blob([JSON.stringify(manifest)], { type: 'application/json' });
            const manifestURL = URL.createObjectURL(manifestBlob);

            const manifestLink = document.getElementById('manifest-placeholder');
            if (manifestLink) {
                manifestLink.href = manifestURL;
            }
        }

        function registerServiceWorker() {
            const swCode = `
                const CACHE_NAME = 'clipboard-v1';
                const urlsToCache = [
                    './',
                    './index.html'
                ];

                self.addEventListener('install', (event) => {
                    event.waitUntil(
                        caches.open(CACHE_NAME)
                            .then((cache) => cache.addAll(urlsToCache))
                    );
                });

                self.addEventListener('fetch', (event) => {
                    event.respondWith(
                        caches.match(event.request)
                            .then((response) => {
                                if (response) {
                                    return response;
                                }
                                return fetch(event.request);
                            })
                    );
                });
            `;

            const swBlob = new Blob([swCode], { type: 'application/javascript' });
            const swURL = URL.createObjectURL(swBlob);

            navigator.serviceWorker.register(swURL)
                .then((registration) => {
                    console.log('Service Worker 注册成功:', registration);
                })
                .catch((error) => {
                    console.log('Service Worker 注册失败:', error);
                });
        }

        function showInstallButton() {
            // 创建安装按钮
            const installButton = document.createElement('button');
            installButton.id = 'installButton';
            installButton.innerHTML = '📱 安装应用';
            installButton.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: var(--button-success);
                color: white;
                border: none;
                border-radius: 50px;
                padding: 12px 20px;
                font-weight: 600;
                cursor: pointer;
                box-shadow: var(--shadow-hover);
                z-index: 10000;
                animation: bounce 2s infinite;
            `;
            installButton.onclick = installApp;

            document.body.appendChild(installButton);
        }

        function hideInstallButton() {
            const installButton = document.getElementById('installButton');
            if (installButton) {
                installButton.remove();
            }
        }

        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        showSuccessMessage('📱 开始安装应用...');
                    }
                    deferredPrompt = null;
                    hideInstallButton();
                });
            }
        }

        // 👆 手势操作系统
        function setupGestureEvents(element, index) {
            let startX = 0;
            let startY = 0;
            let currentX = 0;
            let currentY = 0;
            let isDragging = false;
            let tapCount = 0;
            let tapTimer = null;
            let longPressTimer = null;
            let isLongPress = false;

            // 添加滑动背景
            const swipeBackground = document.createElement('div');
            swipeBackground.className = 'swipe-background';
            swipeBackground.innerHTML = '🗑️ 删除';
            element.style.position = 'relative';
            element.appendChild(swipeBackground);

            // 触摸开始
            element.addEventListener('touchstart', handleTouchStart, { passive: false });
            element.addEventListener('mousedown', handleMouseStart);

            // 触摸移动
            element.addEventListener('touchmove', handleTouchMove, { passive: false });
            element.addEventListener('mousemove', handleMouseMove);

            // 触摸结束
            element.addEventListener('touchend', handleTouchEnd);
            element.addEventListener('mouseup', handleMouseEnd);
            element.addEventListener('mouseleave', handleMouseEnd);

            function handleTouchStart(e) {
                const touch = e.touches[0];
                handleStart(touch.clientX, touch.clientY, e);
            }

            function handleMouseStart(e) {
                handleStart(e.clientX, e.clientY, e);
            }

            function handleStart(x, y, e) {
                startX = x;
                startY = y;
                currentX = x;
                currentY = y;
                isDragging = false;
                isLongPress = false;

                // 长按检测
                longPressTimer = setTimeout(() => {
                    if (!isDragging) {
                        isLongPress = true;
                        showLongPressMenu(e, index);
                        navigator.vibrate && navigator.vibrate(50); // 触觉反馈
                    }
                }, 500);

                // 双击检测
                tapCount++;
                if (tapCount === 1) {
                    tapTimer = setTimeout(() => {
                        tapCount = 0;
                    }, 300);
                } else if (tapCount === 2) {
                    clearTimeout(tapTimer);
                    tapCount = 0;
                    if (!isDragging && !isLongPress) {
                        handleDoubleTap(index);
                    }
                }
            }

            function handleTouchMove(e) {
                if (e.touches.length > 1) return;
                const touch = e.touches[0];
                handleMove(touch.clientX, touch.clientY, e);
            }

            function handleMouseMove(e) {
                if (e.buttons !== 1) return;
                handleMove(e.clientX, e.clientY, e);
            }

            function handleMove(x, y, e) {
                if (isLongPress) return;

                currentX = x;
                currentY = y;

                const deltaX = currentX - startX;
                const deltaY = currentY - startY;

                // 判断是否开始拖拽
                if (!isDragging && (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10)) {
                    isDragging = true;
                    clearTimeout(longPressTimer);
                }

                if (isDragging) {
                    // 水平滑动删除
                    if (Math.abs(deltaX) > Math.abs(deltaY) && deltaX < -20) {
                        e.preventDefault();
                        const swipeDistance = Math.min(Math.abs(deltaX), 80);
                        element.style.transform = `translateX(-${swipeDistance}px)`;

                        if (swipeDistance > 40) {
                            swipeBackground.classList.add('active');
                        } else {
                            swipeBackground.classList.remove('active');
                        }
                    }
                }
            }

            function handleTouchEnd(e) {
                handleEnd();
            }

            function handleMouseEnd(e) {
                handleEnd();
            }

            function handleEnd() {
                clearTimeout(longPressTimer);

                if (isDragging) {
                    const deltaX = currentX - startX;

                    // 滑动删除
                    if (deltaX < -60) {
                        element.style.transform = 'translateX(-100%)';
                        element.style.opacity = '0';
                        setTimeout(() => {
                            deleteSnippet(index);
                        }, 300);
                    } else {
                        // 回弹
                        element.style.transform = '';
                        swipeBackground.classList.remove('active');
                    }
                }

                isDragging = false;
                isLongPress = false;
            }
        }

        function handleDoubleTap(index) {
            // 双击编辑
            const snippets = getSnippets();
            const snippet = snippets.find(s => s.index === index);

            if (snippet) {
                // 高亮效果
                const snippetElement = document.querySelector(`[data-index="${index}"]`);
                if (snippetElement) {
                    snippetElement.classList.add('double-tap-highlight');
                    setTimeout(() => {
                        snippetElement.classList.remove('double-tap-highlight');
                    }, 500);
                }

                // 开始编辑
                editSnippet(index);
                showSuccessMessage('✏️ 双击编辑模式');
            }
        }

        function showLongPressMenu(e, index) {
            // 移除现有菜单
            const existingMenu = document.querySelector('.long-press-menu');
            if (existingMenu) {
                existingMenu.remove();
            }

            const menu = document.createElement('div');
            menu.className = 'long-press-menu';
            menu.style.display = 'block';

            // 获取触摸位置
            const x = e.touches ? e.touches[0].clientX : e.clientX;
            const y = e.touches ? e.touches[0].clientY : e.clientY;

            menu.style.left = `${x}px`;
            menu.style.top = `${y - 100}px`;

            menu.innerHTML = `
                <button onclick="editSnippet(${index}); hideLongPressMenu();">✏️ 编辑</button>
                <button onclick="copySnippet(${index}); hideLongPressMenu();">📋 复制</button>
                <button onclick="togglePin(${index}); hideLongPressMenu();">📌 置顶</button>
                <button onclick="deleteSnippet(${index}); hideLongPressMenu();" style="color: #dc3545;">🗑️ 删除</button>
            `;

            document.body.appendChild(menu);

            // 点击其他地方关闭菜单
            setTimeout(() => {
                document.addEventListener('click', hideLongPressMenu, { once: true });
                document.addEventListener('touchstart', hideLongPressMenu, { once: true });
            }, 100);
        }

        function hideLongPressMenu() {
            const menu = document.querySelector('.long-press-menu');
            if (menu) {
                menu.remove();
            }
        }

        // 增强的复制功能
        function copySnippet(index) {
            const snippets = getSnippets();
            const snippet = snippets.find(s => s.index === index);

            if (snippet) {
                // 提取纯文本
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = snippet.content;
                const plainText = tempDiv.textContent || tempDiv.innerText || '';

                navigator.clipboard.writeText(plainText)
                    .then(() => {
                        showSuccessMessage('📋 内容已复制到剪贴板！');

                        // 视觉反馈
                        const snippetElement = document.querySelector(`[data-index="${index}"]`);
                        if (snippetElement) {
                            snippetElement.style.background = 'rgba(16, 185, 129, 0.1)';
                            setTimeout(() => {
                                snippetElement.style.background = '';
                            }, 1000);
                        }
                    })
                    .catch(() => {
                        showSuccessMessage('❌ 复制失败');
                    });
            }
        }

        // 增强的置顶切换
        function togglePin(index) {
            let snippets = getSnippets();
            const snippet = snippets.find(s => s.index === index);

            if (snippet) {
                snippet.pinned = !snippet.pinned;

                if (saveSnippets(snippets)) {
                    const action = snippet.pinned ? '📌 已置顶' : '📌 已取消置顶';
                    showSuccessMessage(action);
                    loadSnippets();
                }
            }
        }

        // 🔄 撤销重做系统
        function saveStateForUndo(action, data) {
            const state = {
                action: action,
                data: JSON.parse(JSON.stringify(data)), // 深拷贝
                timestamp: Date.now(),
                snippets: JSON.parse(JSON.stringify(getSnippets())) // 保存当前状态
            };

            undoStack.push(state);

            // 限制撤销栈大小
            if (undoStack.length > MAX_UNDO_STEPS) {
                undoStack.shift();
            }

            // 清空重做栈
            redoStack = [];

            updateUndoRedoButtons();
        }

        function undo() {
            if (undoStack.length === 0) {
                showSuccessMessage('没有可撤销的操作');
                return;
            }

            const currentState = {
                action: 'current',
                data: null,
                timestamp: Date.now(),
                snippets: JSON.parse(JSON.stringify(getSnippets()))
            };

            redoStack.push(currentState);

            const lastState = undoStack.pop();

            // 恢复到上一个状态
            if (saveSnippets(lastState.snippets)) {
                showSuccessMessage(`✅ 已撤销: ${getActionName(lastState.action)}`);
                loadSnippets();
                updateUndoRedoButtons();
            }
        }

        function redo() {
            if (redoStack.length === 0) {
                showSuccessMessage('没有可重做的操作');
                return;
            }

            const currentState = {
                action: 'current',
                data: null,
                timestamp: Date.now(),
                snippets: JSON.parse(JSON.stringify(getSnippets()))
            };

            undoStack.push(currentState);

            const nextState = redoStack.pop();

            // 恢复到下一个状态
            if (saveSnippets(nextState.snippets)) {
                showSuccessMessage(`✅ 已重做操作`);
                loadSnippets();
                updateUndoRedoButtons();
            }
        }

        function getActionName(action) {
            const actionNames = {
                'add': '添加内容',
                'delete': '删除内容',
                'edit': '编辑内容',
                'move': '移动内容',
                'pin': '置顶操作',
                'batch_delete': '批量删除',
                'batch_move': '批量移动',
                'reorder': '重新排序'
            };
            return actionNames[action] || '未知操作';
        }

        function updateUndoRedoButtons() {
            // 这里可以更新撤销重做按钮的状态
            // 如果有UI按钮的话
        }

        // 修改现有的操作函数，添加撤销支持
        function addSnippetWithUndo(position) {
            const beforeState = getSnippets();
            addSnippet(position);
            const afterState = getSnippets();

            if (afterState.length > beforeState.length) {
                saveStateForUndo('add', { position: position });
            }
        }

        function deleteSnippetWithUndo(index) {
            const snippets = getSnippets();
            const snippetToDelete = snippets.find(s => s.index === index);

            if (snippetToDelete && confirm('确定要删除这条记录吗？')) {
                saveStateForUndo('delete', { snippet: snippetToDelete, index: index });

                let newSnippets = snippets.filter(s => s.index !== index);
                newSnippets.forEach((s, i) => s.index = i);

                if (saveSnippets(newSnippets)) {
                    showSuccessMessage('删除成功！');
                    loadSnippets();
                }
            }
        }
    </script>
</body>
</html> 
</html> 