#!/usr/bin/env python3
"""
简单的Web界面启动器
"""

import http.server
import socketserver
import webbrowser
import threading
import time

# 简单的HTML页面
HTML_CONTENT = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComfyUI算力节点</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px;
            font-size: 16px;
            border: none;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .status {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 ComfyUI算力节点管理</h1>
            <p>云端AI绘画算力节点控制面板</p>
        </div>
        
        <div class="status">
            <h3>📊 服务状态</h3>
            <p><strong>ComfyUI服务:</strong> https://koomfonjr8-8188.cnb.run</p>
            <p><strong>本地管理:</strong> http://localhost:8080</p>
            <p><strong>状态:</strong> <span style="color: green;">✅ 在线</span></p>
        </div>
        
        <div style="text-align: center;">
            <a href="https://koomfonjr8-8188.cnb.run" target="_blank" class="btn">
                🌐 打开ComfyUI界面
            </a>
            <button class="btn" onclick="testConnection()">
                🔍 测试连接
            </button>
            <button class="btn" onclick="startNode()">
                🚀 启动节点
            </button>
        </div>
        
        <div class="grid">
            <div class="card">
                <h4>📋 快速测试</h4>
                <p>测试ComfyUI服务连接</p>
                <button class="btn" onclick="runTest()">运行测试</button>
            </div>
            <div class="card">
                <h4>🎨 生成测试</h4>
                <p>测试图像生成功能</p>
                <button class="btn" onclick="generateTest()">生成图片</button>
            </div>
            <div class="card">
                <h4>📊 节点状态</h4>
                <p>查看算力节点状态</p>
                <button class="btn" onclick="checkStatus()">检查状态</button>
            </div>
        </div>
        
        <div class="status">
            <h3>📝 操作日志</h3>
            <div id="log" style="background: #2d3748; color: white; padding: 15px; border-radius: 5px; font-family: monospace; height: 200px; overflow-y: auto;">
                <div>🚀 Web管理界面已启动</div>
                <div>📡 ComfyUI服务地址: https://koomfonjr8-8188.cnb.run</div>
                <div>✅ 系统就绪，可以开始使用</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <h3>🛠️ 开发者工具</h3>
            <p>在VSCode终端中运行以下命令：</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; text-align: left;">
                # 测试连接<br>
                python simple_test.py<br><br>
                # 测试生成<br>
                python test_with_default_model.py<br><br>
                # 启动节点<br>
                python start_comfyui_node.py
            </div>
        </div>
    </div>

    <script>
        function addLog(message) {
            const log = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            log.innerHTML += '<div>' + time + ' ' + message + '</div>';
            log.scrollTop = log.scrollHeight;
        }
        
        function testConnection() {
            addLog('🔍 开始测试ComfyUI连接...');
            fetch('https://koomfonjr8-8188.cnb.run')
                .then(response => {
                    if (response.ok) {
                        addLog('✅ ComfyUI连接成功');
                    } else {
                        addLog('❌ ComfyUI连接失败: ' + response.status);
                    }
                })
                .catch(error => {
                    addLog('❌ 连接错误: ' + error.message);
                });
        }
        
        function startNode() {
            addLog('🚀 准备启动算力节点...');
            addLog('💡 请在VSCode终端中运行: python start_comfyui_node.py');
        }
        
        function runTest() {
            addLog('📋 准备运行连接测试...');
            addLog('💡 请在VSCode终端中运行: python simple_test.py');
        }
        
        function generateTest() {
            addLog('🎨 准备运行生成测试...');
            addLog('💡 请在VSCode终端中运行: python test_with_default_model.py');
        }
        
        function checkStatus() {
            addLog('📊 检查系统状态...');
            addLog('✅ Web界面运行正常');
            addLog('🌐 ComfyUI服务可访问');
            addLog('💻 开发环境就绪');
        }
        
        // 页面加载完成后自动测试连接
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
"""

class SimpleHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(HTML_CONTENT.encode('utf-8'))
        else:
            super().do_GET()
    
    def log_message(self, format, *args):
        # 禁用默认日志
        pass

def start_server():
    PORT = 8080
    try:
        with socketserver.TCPServer(("", PORT), SimpleHandler) as httpd:
            print(f"🌐 Web管理界面启动成功!")
            print(f"📱 访问地址: http://localhost:{PORT}")
            print(f"🎨 ComfyUI地址: https://koomfonjr8-8188.cnb.run")
            print("按 Ctrl+C 停止服务")
            
            # 自动打开浏览器
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{PORT}')
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Web服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    start_server()
