#!/usr/bin/env python3
"""
简单的ComfyUI连接测试
不需要额外依赖，使用Python标准库
"""

import urllib.request
import json
import sys

def test_comfyui_simple():
    """简单测试ComfyUI连接"""
    server_url = "https://koomfonjr8-8188.cnb.run"
    
    print("🚀 简单ComfyUI连接测试")
    print("=" * 50)
    
    # 1. 测试基本连接
    print("1️⃣ 测试基本连接...")
    try:
        with urllib.request.urlopen(server_url, timeout=10) as response:
            status_code = response.getcode()
            content = response.read().decode('utf-8')
            
            print(f"   状态码: {status_code}")
            print(f"   响应长度: {len(content)} 字符")
            
            if status_code == 200:
                if "ComfyUI" in content:
                    print("   ✅ 确认是ComfyUI服务")
                else:
                    print("   ⚠️ 可能不是ComfyUI服务")
                    print(f"   前100字符: {content[:100]}...")
            else:
                print(f"   ❌ 连接失败: {status_code}")
                return False
                
    except Exception as e:
        print(f"   ❌ 连接异常: {e}")
        return False
    
    # 2. 测试object_info API
    print("\n2️⃣ 测试object_info API...")
    try:
        api_url = f"{server_url}/object_info"
        with urllib.request.urlopen(api_url, timeout=10) as response:
            status_code = response.getcode()
            print(f"   状态码: {status_code}")
            
            if status_code == 200:
                data = json.loads(response.read().decode('utf-8'))
                print(f"   ✅ API正常，包含 {len(data)} 个节点类型")
                
                # 检查关键节点
                key_nodes = ["CheckpointLoaderSimple", "VAELoader", "CLIPTextEncode", "KSampler", "SaveImage"]
                print("   🔧 关键节点检查:")
                
                for node in key_nodes:
                    if node in data:
                        print(f"      ✅ {node}")
                    else:
                        print(f"      ❌ {node} (缺失)")
                
                # 检查模型
                if "CheckpointLoaderSimple" in data:
                    print("\n   📦 检查可用模型...")
                    loader = data["CheckpointLoaderSimple"]
                    
                    if "input" in loader and "ckpt_name" in loader["input"]:
                        ckpt_info = loader["input"]["ckpt_name"]
                        print(f"      ckpt_name数据: {ckpt_info}")
                        
                        # 解析模型列表
                        models = []
                        if isinstance(ckpt_info, list) and len(ckpt_info) > 0:
                            if isinstance(ckpt_info[0], list):
                                models = ckpt_info[0]
                            else:
                                models = ckpt_info
                        
                        print(f"      📋 找到 {len(models)} 个模型")
                        if models:
                            print("      📋 可用模型:")
                            for i, model in enumerate(models[:5]):
                                print(f"         {i+1}. {model}")
                            if len(models) > 5:
                                print(f"         ... 还有 {len(models) - 5} 个")
                        else:
                            print("      ⚠️ 没有检测到模型")
                    else:
                        print("      ❌ 没有ckpt_name参数")
                else:
                    print("   ❌ 没有CheckpointLoaderSimple节点")
                
                return True
            else:
                print(f"   ❌ API调用失败: {status_code}")
                return False
                
    except Exception as e:
        print(f"   ❌ API异常: {e}")
        return False
    
    # 3. 测试队列API
    print("\n3️⃣ 测试队列API...")
    try:
        queue_url = f"{server_url}/queue"
        with urllib.request.urlopen(queue_url, timeout=10) as response:
            status_code = response.getcode()
            print(f"   状态码: {status_code}")
            
            if status_code == 200:
                queue_data = json.loads(response.read().decode('utf-8'))
                print(f"   ✅ 队列API正常")
                print(f"   📊 队列信息: {queue_data}")
            else:
                print(f"   ❌ 队列API失败: {status_code}")
                
    except Exception as e:
        print(f"   ❌ 队列API异常: {e}")

def main():
    """主函数"""
    try:
        success = test_comfyui_simple()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 基本测试通过！")
            print("\n📝 接下来您可以:")
            print("1. 安装完整依赖: pip install aiohttp websockets PyYAML")
            print("2. 运行完整测试: python test_comfyui.py")
            print("3. 启动算力节点: python start_comfyui_node.py")
        else:
            print("❌ 测试失败，请检查网络连接和服务状态")
            
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
