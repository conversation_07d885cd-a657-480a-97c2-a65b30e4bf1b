# Dreamify AI服务依赖包

# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# AI和机器学习
torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0
diffusers>=0.24.0
transformers>=4.35.0
accelerate>=0.24.0
safetensors>=0.4.0
xformers>=0.0.22  # 可选，需要CUDA支持

# 图像处理
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0
scipy>=1.11.0

# 网络和异步
aiohttp>=3.9.0
aiofiles>=23.2.0
httpx>=0.25.0
websockets>=12.0

# 数据处理和序列化
pandas>=2.1.0
pydantic-settings>=2.1.0
python-multipart>=0.0.6

# Redis客户端
redis>=5.0.0
aioredis>=2.0.0

# 配置和环境
python-dotenv>=1.0.0
PyYAML>=6.0.1
click>=8.1.0

# 日志和监控
loguru>=0.7.0
prometheus-client>=0.19.0

# 系统监控
psutil>=5.9.0
GPUtil>=1.4.0
nvidia-ml-py>=12.535.0

# 开发和测试工具
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.9.0
flake8>=6.1.0
mypy>=1.6.0

# 可选：高级功能
# controlnet-aux>=0.0.7  # ControlNet支持
# compel>=2.0.2          # 提示词增强
# invisible-watermark>=0.2.0  # 水印功能
# triton>=2.1.0          # GPU优化，需要CUDA支持
