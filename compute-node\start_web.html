<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComfyUI算力节点管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .content {
            padding: 40px;
        }
        
        .status-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .status-card h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.7);
            border-radius: 10px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .metric {
            text-align: center;
            padding: 20px;
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .metric-label {
            color: #666;
            font-size: 1.1em;
        }
        
        .connection-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-testing {
            background: #fff3cd;
            color: #856404;
        }
        
        .commands {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .command-line {
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 5px 0;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .command-line:hover {
            background: #4a5568;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 ComfyUI算力节点管理中心</h1>
            <p>云端AI绘画算力节点控制面板</p>
        </div>
        
        <div class="content">
            <!-- 连接状态 -->
            <div class="status-card">
                <h3>🔗 连接状态</h3>
                <div class="status-item">
                    <span><strong>ComfyUI云端服务:</strong></span>
                    <span>
                        https://koomfonjr8-8188.cnb.run
                        <span id="comfyui-status" class="connection-status status-testing">检测中...</span>
                    </span>
                </div>
                <div class="status-item">
                    <span><strong>本地管理界面:</strong></span>
                    <span>
                        http://localhost:8080
                        <span class="connection-status status-online">✅ 在线</span>
                    </span>
                </div>
                <div class="status-item">
                    <span><strong>算力节点状态:</strong></span>
                    <span id="node-status">
                        <span class="connection-status status-offline">❌ 未启动</span>
                    </span>
                </div>
            </div>
            
            <!-- 快速操作 -->
            <div style="text-align: center; margin: 30px 0;">
                <a href="https://koomfonjr8-8188.cnb.run" target="_blank" class="btn btn-info">
                    🌐 打开ComfyUI界面
                </a>
                <button class="btn btn-success" onclick="testConnection()">
                    🔍 测试云端连接
                </button>
                <button class="btn btn-warning" onclick="showCommands()">
                    🚀 启动算力节点
                </button>
            </div>
            
            <!-- 功能卡片 -->
            <div class="grid">
                <div class="card">
                    <h4>📡 连接测试</h4>
                    <div class="metric">
                        <div class="metric-value" id="ping-time">--</div>
                        <div class="metric-label">响应时间 (ms)</div>
                    </div>
                    <button class="btn" onclick="runConnectionTest()">运行测试</button>
                </div>
                
                <div class="card">
                    <h4>🎨 图像生成</h4>
                    <div class="metric">
                        <div class="metric-value" id="generation-count">0</div>
                        <div class="metric-label">已生成图片</div>
                    </div>
                    <button class="btn" onclick="testGeneration()">测试生成</button>
                </div>
                
                <div class="card">
                    <h4>⚡ 算力状态</h4>
                    <div class="metric">
                        <div class="metric-value" id="node-count">0</div>
                        <div class="metric-label">活跃节点</div>
                    </div>
                    <button class="btn" onclick="checkNodes()">检查节点</button>
                </div>
            </div>
            
            <!-- 命令行指令 -->
            <div class="commands" id="commands-section" style="display: none;">
                <h3>💻 在VSCode终端中运行以下命令：</h3>
                <div class="command-line" onclick="copyCommand(this)" title="点击复制">
                    <strong>1. 测试连接:</strong> python simple_test.py
                </div>
                <div class="command-line" onclick="copyCommand(this)" title="点击复制">
                    <strong>2. 测试生成:</strong> python test_with_default_model.py
                </div>
                <div class="command-line" onclick="copyCommand(this)" title="点击复制">
                    <strong>3. 启动节点:</strong> python start_comfyui_node.py
                </div>
                <div class="command-line" onclick="copyCommand(this)" title="点击复制">
                    <strong>4. Web界面:</strong> python simple_web.py
                </div>
                <p style="margin-top: 15px; color: #666;">💡 提示：点击命令行可复制到剪贴板</p>
            </div>
            
            <!-- 操作日志 -->
            <div>
                <h3>📝 操作日志</h3>
                <div id="log" class="log-container">
                    <div>🚀 ComfyUI算力节点管理界面已启动</div>
                    <div>📡 云端服务地址: https://koomfonjr8-8188.cnb.run</div>
                    <div>💻 本地管理地址: http://localhost:8080</div>
                    <div>✅ 系统就绪，可以开始使用</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加日志
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            log.innerHTML += `<div>${time} ${icon} ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        // 测试ComfyUI连接
        function testConnection() {
            addLog('🔍 开始测试ComfyUI云端连接...');
            document.getElementById('comfyui-status').textContent = '检测中...';
            document.getElementById('comfyui-status').className = 'connection-status status-testing';
            
            const startTime = Date.now();
            
            fetch('https://koomfonjr8-8188.cnb.run', {
                method: 'GET',
                mode: 'no-cors'
            })
            .then(() => {
                const pingTime = Date.now() - startTime;
                document.getElementById('ping-time').textContent = pingTime;
                document.getElementById('comfyui-status').textContent = '✅ 在线';
                document.getElementById('comfyui-status').className = 'connection-status status-online';
                addLog(`✅ ComfyUI连接成功，响应时间: ${pingTime}ms`, 'success');
            })
            .catch(error => {
                document.getElementById('comfyui-status').textContent = '❌ 离线';
                document.getElementById('comfyui-status').className = 'connection-status status-offline';
                addLog('❌ ComfyUI连接失败: ' + error.message, 'error');
            });
        }
        
        // 运行连接测试
        function runConnectionTest() {
            addLog('📡 运行详细连接测试...');
            addLog('💡 请在VSCode终端中运行: python simple_test.py', 'warning');
        }
        
        // 测试图像生成
        function testGeneration() {
            addLog('🎨 准备测试图像生成功能...');
            addLog('💡 请在VSCode终端中运行: python test_with_default_model.py', 'warning');
            
            // 模拟增加生成计数
            const currentCount = parseInt(document.getElementById('generation-count').textContent);
            document.getElementById('generation-count').textContent = currentCount + 1;
        }
        
        // 检查节点状态
        function checkNodes() {
            addLog('⚡ 检查算力节点状态...');
            addLog('💡 请在VSCode终端中运行: python start_comfyui_node.py', 'warning');
            
            // 模拟节点状态
            document.getElementById('node-count').textContent = '1';
            document.getElementById('node-status').innerHTML = '<span class="connection-status status-online">✅ 运行中</span>';
        }
        
        // 显示命令
        function showCommands() {
            const section = document.getElementById('commands-section');
            section.style.display = section.style.display === 'none' ? 'block' : 'none';
            addLog('💻 显示启动命令，请在VSCode终端中执行');
        }
        
        // 复制命令
        function copyCommand(element) {
            const text = element.textContent.split(': ')[1];
            navigator.clipboard.writeText(text).then(() => {
                addLog(`📋 已复制命令: ${text}`, 'success');
            }).catch(() => {
                addLog('❌ 复制失败，请手动复制', 'error');
            });
        }
        
        // 页面加载完成后自动测试连接
        window.onload = function() {
            addLog('🌐 页面加载完成，开始自动检测...');
            setTimeout(testConnection, 1000);
            
            // 每30秒自动检测一次
            setInterval(testConnection, 30000);
        };
    </script>
</body>
</html>
