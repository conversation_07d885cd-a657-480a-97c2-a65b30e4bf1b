# Dreamify 算力节点配置文件

# 节点基本信息
node_id: ""  # 留空将自动生成，或手动设置如 "node-001"
node_name: "RTX4090-Node-1"  # 节点显示名称

# 调度中心连接
scheduler_url: "http://localhost:8001"  # 调度中心地址

# GPU硬件信息
gpu_model: "RTX 4090"  # GPU型号
gpu_memory: 24576  # GPU显存大小(MB)

# 任务处理配置
max_concurrent_tasks: 4  # 最大并发任务数
task_timeout: 300  # 任务超时时间(秒)

# 支持的AI模型列表
supported_models:
  - "stable-diffusion-xl"
  - "flux-dev"
  # - "custom-model"  # 可添加自定义模型

# 网络配置
heartbeat_interval: 30  # 心跳间隔(秒)
connection_timeout: 30  # 连接超时(秒)

# 存储配置
model_cache_dir: "./models"  # 模型缓存目录
temp_dir: "./temp"  # 临时文件目录

# 日志配置
log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR
log_file: "./logs/node.log"  # 日志文件路径

# 性能优化配置
torch_compile: false  # 是否启用torch.compile优化
memory_fraction: 0.9  # GPU显存使用比例
precision: "fp16"  # 计算精度: fp16, fp32, bf16

# 安全配置
api_key: ""  # API密钥（如果调度中心需要）
ssl_verify: true  # 是否验证SSL证书

# 监控配置
enable_metrics: true  # 是否启用性能监控
metrics_port: 9100  # 监控指标端口

# 高级配置
auto_restart: true  # 任务失败时是否自动重启
max_retries: 3  # 最大重试次数
cleanup_interval: 3600  # 清理临时文件间隔(秒)
