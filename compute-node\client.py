#!/usr/bin/env python3
"""
Dreamify 算力节点客户端
负责连接到调度中心，接收和处理AI图像生成任务
"""

import asyncio
import aiohttp
import json
import logging
import os
import sys
import time
import uuid
import psutil
import <PERSON><PERSON><PERSON>
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import yaml
import signal

# AI模型相关导入
import torch
from diffusers import StableDiffusionXLPipeline, FluxPipeline
from PIL import Image
import io
import base64

@dataclass
class NodeConfig:
    """节点配置"""
    node_id: str
    node_name: str
    scheduler_url: str
    gpu_model: str
    gpu_memory: int
    max_concurrent_tasks: int
    supported_models: list
    heartbeat_interval: int = 30
    task_timeout: int = 300
    model_cache_dir: str = "./models"

@dataclass
class NodeStatus:
    """节点状态"""
    node_id: str
    status: str  # online, offline, busy, error
    gpu_usage: float
    gpu_memory_used: float
    gpu_memory_total: float
    cpu_usage: float
    memory_usage: float
    active_tasks: int
    total_tasks_completed: int
    last_heartbeat: float

class ComputeNodeClient:
    """算力节点客户端"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config = self._load_config(config_path)
        self.status = NodeStatus(
            node_id=self.config.node_id,
            status="offline",
            gpu_usage=0.0,
            gpu_memory_used=0.0,
            gpu_memory_total=0.0,
            cpu_usage=0.0,
            memory_usage=0.0,
            active_tasks=0,
            total_tasks_completed=0,
            last_heartbeat=0.0
        )
        
        self.session: Optional[aiohttp.ClientSession] = None
        self.models: Dict[str, Any] = {}
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.running = False
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(f"ComputeNode-{self.config.node_id}")
        
    def _load_config(self, config_path: str) -> NodeConfig:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 生成唯一节点ID（如果没有配置）
            if not config_data.get('node_id'):
                config_data['node_id'] = f"node-{uuid.uuid4().hex[:8]}"
            
            return NodeConfig(**config_data)
        except FileNotFoundError:
            self.logger.error(f"配置文件 {config_path} 不存在")
            sys.exit(1)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            sys.exit(1)
    
    async def initialize(self):
        """初始化节点"""
        self.logger.info(f"初始化算力节点: {self.config.node_name}")
        
        # 创建HTTP会话
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        
        # 检查GPU
        await self._check_gpu()
        
        # 加载AI模型
        await self._load_models()
        
        # 注册到调度中心
        await self._register_node()
        
        self.running = True
        self.logger.info("节点初始化完成")
    
    async def _check_gpu(self):
        """检查GPU状态"""
        try:
            gpus = GPUtil.getGPUs()
            if not gpus:
                raise RuntimeError("未检测到GPU")
            
            gpu = gpus[0]  # 使用第一个GPU
            self.status.gpu_memory_total = gpu.memoryTotal
            self.logger.info(f"检测到GPU: {gpu.name}, 显存: {gpu.memoryTotal}MB")
            
        except Exception as e:
            self.logger.error(f"GPU检查失败: {e}")
            raise
    
    async def _load_models(self):
        """加载AI模型"""
        self.logger.info("开始加载AI模型...")
        
        model_cache_dir = Path(self.config.model_cache_dir)
        model_cache_dir.mkdir(exist_ok=True)
        
        try:
            # 加载Stable Diffusion XL
            if "stable-diffusion-xl" in self.config.supported_models:
                self.logger.info("加载 Stable Diffusion XL...")
                self.models["stable-diffusion-xl"] = StableDiffusionXLPipeline.from_pretrained(
                    "stabilityai/stable-diffusion-xl-base-1.0",
                    torch_dtype=torch.float16,
                    cache_dir=str(model_cache_dir),
                    use_safetensors=True
                ).to("cuda")
                self.logger.info("Stable Diffusion XL 加载完成")
            
            # 加载FLUX模型
            if "flux-dev" in self.config.supported_models:
                self.logger.info("加载 FLUX Dev...")
                self.models["flux-dev"] = FluxPipeline.from_pretrained(
                    "black-forest-labs/FLUX.1-dev",
                    torch_dtype=torch.bfloat16,
                    cache_dir=str(model_cache_dir)
                ).to("cuda")
                self.logger.info("FLUX Dev 加载完成")
                
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
    
    async def _register_node(self):
        """注册节点到调度中心"""
        registration_data = {
            "node_id": self.config.node_id,
            "node_name": self.config.node_name,
            "gpu_model": self.config.gpu_model,
            "gpu_memory": self.config.gpu_memory,
            "max_concurrent_tasks": self.config.max_concurrent_tasks,
            "supported_models": self.config.supported_models,
            "status": "online"
        }
        
        try:
            async with self.session.post(
                f"{self.config.scheduler_url}/api/nodes/register",
                json=registration_data
            ) as response:
                if response.status == 200:
                    self.logger.info("节点注册成功")
                    self.status.status = "online"
                else:
                    error_text = await response.text()
                    raise RuntimeError(f"节点注册失败: {response.status} - {error_text}")
                    
        except Exception as e:
            self.logger.error(f"节点注册失败: {e}")
            raise
    
    async def _update_status(self):
        """更新节点状态"""
        try:
            # 获取系统状态
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # 获取GPU状态
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]
                self.status.gpu_usage = gpu.load * 100
                self.status.gpu_memory_used = gpu.memoryUsed
                self.status.gpu_memory_total = gpu.memoryTotal
            
            self.status.cpu_usage = cpu_percent
            self.status.memory_usage = memory.percent
            self.status.active_tasks = len(self.active_tasks)
            self.status.last_heartbeat = time.time()
            
            # 根据任务负载更新状态
            if self.status.active_tasks >= self.config.max_concurrent_tasks:
                self.status.status = "busy"
            elif self.status.active_tasks > 0:
                self.status.status = "processing"
            else:
                self.status.status = "online"
                
        except Exception as e:
            self.logger.error(f"状态更新失败: {e}")
            self.status.status = "error"
    
    async def _send_heartbeat(self):
        """发送心跳到调度中心"""
        try:
            await self._update_status()
            
            async with self.session.post(
                f"{self.config.scheduler_url}/api/nodes/{self.config.node_id}/heartbeat",
                json=asdict(self.status)
            ) as response:
                if response.status != 200:
                    self.logger.warning(f"心跳发送失败: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"心跳发送失败: {e}")
    
    async def _poll_tasks(self):
        """轮询获取新任务"""
        try:
            if len(self.active_tasks) >= self.config.max_concurrent_tasks:
                return  # 已达到最大并发数
            
            async with self.session.get(
                f"{self.config.scheduler_url}/api/nodes/{self.config.node_id}/tasks"
            ) as response:
                if response.status == 200:
                    tasks = await response.json()
                    for task_data in tasks.get('tasks', []):
                        await self._process_task(task_data)
                        
        except Exception as e:
            self.logger.error(f"任务轮询失败: {e}")
    
    async def _process_task(self, task_data: Dict[str, Any]):
        """处理单个任务"""
        task_id = task_data['id']
        
        if task_id in self.active_tasks:
            return  # 任务已在处理中
        
        self.logger.info(f"开始处理任务: {task_id}")
        
        # 创建任务协程
        task_coroutine = self._execute_task(task_data)
        task = asyncio.create_task(task_coroutine)
        self.active_tasks[task_id] = task
        
        # 任务完成后清理
        def task_done_callback(task):
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            self.status.total_tasks_completed += 1
        
        task.add_done_callback(task_done_callback)
    
    async def _execute_task(self, task_data: Dict[str, Any]):
        """执行AI图像生成任务"""
        task_id = task_data['id']
        
        try:
            # 报告任务开始
            await self._report_task_status(task_id, "processing", 0)
            
            # 获取任务参数
            prompt = task_data['prompt']
            model_name = task_data['model']
            width = task_data.get('width', 1024)
            height = task_data.get('height', 1024)
            steps = task_data.get('steps', 30)
            batch_size = task_data.get('batch_size', 1)
            cfg_scale = task_data.get('cfg_scale', 7.5)
            seed = task_data.get('seed', -1)
            negative_prompt = task_data.get('negative_prompt', '')
            
            # 检查模型是否可用
            if model_name not in self.models:
                raise ValueError(f"不支持的模型: {model_name}")
            
            model = self.models[model_name]
            
            # 设置随机种子
            if seed == -1:
                seed = torch.randint(0, 2**32 - 1, (1,)).item()
            
            generator = torch.Generator(device="cuda").manual_seed(seed)
            
            # 生成图像
            self.logger.info(f"生成图像: {prompt[:50]}...")
            
            # 进度回调
            def progress_callback(step, timestep, latents):
                progress = (step / steps) * 100
                asyncio.create_task(self._report_task_status(task_id, "processing", progress))
            
            # 执行生成
            with torch.no_grad():
                result = model(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    width=width,
                    height=height,
                    num_inference_steps=steps,
                    num_images_per_prompt=batch_size,
                    guidance_scale=cfg_scale,
                    generator=generator,
                    callback=progress_callback,
                    callback_steps=1
                )
            
            # 处理生成的图像
            images = []
            for i, image in enumerate(result.images):
                # 转换为base64
                buffer = io.BytesIO()
                image.save(buffer, format='PNG')
                image_base64 = base64.b64encode(buffer.getvalue()).decode()
                images.append({
                    'index': i,
                    'data': image_base64,
                    'format': 'png'
                })
            
            # 报告任务完成
            await self._report_task_completion(task_id, images, {
                'seed': seed,
                'model': model_name,
                'steps': steps,
                'cfg_scale': cfg_scale
            })
            
            self.logger.info(f"任务完成: {task_id}")
            
        except Exception as e:
            self.logger.error(f"任务执行失败 {task_id}: {e}")
            await self._report_task_error(task_id, str(e))
    
    async def _report_task_status(self, task_id: str, status: str, progress: float):
        """报告任务状态"""
        try:
            data = {
                'task_id': task_id,
                'status': status,
                'progress': progress,
                'node_id': self.config.node_id,
                'timestamp': time.time()
            }
            
            async with self.session.post(
                f"{self.config.scheduler_url}/api/tasks/{task_id}/status",
                json=data
            ) as response:
                if response.status != 200:
                    self.logger.warning(f"状态报告失败: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"状态报告失败: {e}")
    
    async def _report_task_completion(self, task_id: str, images: list, metadata: dict):
        """报告任务完成"""
        try:
            data = {
                'task_id': task_id,
                'status': 'completed',
                'progress': 100,
                'images': images,
                'metadata': metadata,
                'node_id': self.config.node_id,
                'timestamp': time.time()
            }
            
            async with self.session.post(
                f"{self.config.scheduler_url}/api/tasks/{task_id}/complete",
                json=data
            ) as response:
                if response.status != 200:
                    self.logger.warning(f"完成报告失败: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"完成报告失败: {e}")
    
    async def _report_task_error(self, task_id: str, error_message: str):
        """报告任务错误"""
        try:
            data = {
                'task_id': task_id,
                'status': 'failed',
                'error': error_message,
                'node_id': self.config.node_id,
                'timestamp': time.time()
            }
            
            async with self.session.post(
                f"{self.config.scheduler_url}/api/tasks/{task_id}/error",
                json=data
            ) as response:
                if response.status != 200:
                    self.logger.warning(f"错误报告失败: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"错误报告失败: {e}")
    
    async def run(self):
        """运行节点主循环"""
        self.logger.info("启动算力节点...")
        
        try:
            await self.initialize()
            
            # 主循环
            while self.running:
                # 发送心跳
                await self._send_heartbeat()
                
                # 轮询任务
                await self._poll_tasks()
                
                # 等待下一个周期
                await asyncio.sleep(self.config.heartbeat_interval)
                
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在关闭...")
        except Exception as e:
            self.logger.error(f"节点运行错误: {e}")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """关闭节点"""
        self.logger.info("正在关闭算力节点...")
        self.running = False
        
        # 等待活动任务完成
        if self.active_tasks:
            self.logger.info(f"等待 {len(self.active_tasks)} 个任务完成...")
            await asyncio.gather(*self.active_tasks.values(), return_exceptions=True)
        
        # 注销节点
        if self.session:
            try:
                async with self.session.post(
                    f"{self.config.scheduler_url}/api/nodes/{self.config.node_id}/unregister"
                ) as response:
                    if response.status == 200:
                        self.logger.info("节点注销成功")
            except Exception as e:
                self.logger.error(f"节点注销失败: {e}")
            
            await self.session.close()
        
        self.logger.info("算力节点已关闭")

def main():
    """主函数"""
    # 处理信号
    def signal_handler(signum, frame):
        print("\n收到退出信号，正在关闭...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并运行节点
    client = ComputeNodeClient()
    
    try:
        asyncio.run(client.run())
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
