"""
AI模型管理器
负责加载、卸载和管理不同的AI模型
"""

import asyncio
import logging
import torch
from typing import Dict, Any, Optional, List
from pathlib import Path
import yaml
import gc

from .stable_diffusion import StableDiffusionModel
from .flux import FluxModel
from ..utils.config import Config

logger = logging.getLogger(__name__)

class ModelManager:
    """AI模型管理器"""
    
    def __init__(self):
        self.loaded_models: Dict[str, Any] = {}
        self.available_models: Dict[str, Dict] = {}
        self.model_configs_path = Path("models/configs")
        self.model_cache_dir = Path(Config.MODEL_CACHE_DIR)
        self.max_loaded_models = Config.MAX_LOADED_MODELS
        
        # 确保目录存在
        self.model_cache_dir.mkdir(parents=True, exist_ok=True)
        
    async def initialize(self):
        """初始化模型管理器"""
        logger.info("初始化模型管理器...")
        
        # 加载模型配置
        await self._load_model_configs()
        
        # 预加载默认模型
        default_models = Config.DEFAULT_MODELS
        for model_id in default_models:
            if model_id in self.available_models:
                try:
                    await self.load_model(model_id)
                    logger.info(f"预加载模型成功: {model_id}")
                except Exception as e:
                    logger.error(f"预加载模型失败 {model_id}: {e}")
        
        logger.info(f"模型管理器初始化完成，已加载 {len(self.loaded_models)} 个模型")
    
    async def _load_model_configs(self):
        """加载模型配置文件"""
        # 内置模型配置
        self.available_models = {
            "stable-diffusion-xl": {
                "name": "Stable Diffusion XL",
                "description": "高质量通用图像生成模型",
                "type": "diffusion",
                "class": "StableDiffusionModel",
                "model_id": "stabilityai/stable-diffusion-xl-base-1.0",
                "supported_sizes": [(1024, 1024), (1152, 896), (896, 1152), (1344, 768), (768, 1344)],
                "max_batch_size": 4,
                "memory_requirement": 6.0,  # GB
                "precision": "fp16"
            },
            "flux-dev": {
                "name": "FLUX Dev",
                "description": "最新的高质量图像生成模型",
                "type": "diffusion",
                "class": "FluxModel",
                "model_id": "black-forest-labs/FLUX.1-dev",
                "supported_sizes": [(1024, 1024), (1152, 896), (896, 1152)],
                "max_batch_size": 2,
                "memory_requirement": 12.0,  # GB
                "precision": "bf16"
            },
            "stable-diffusion-v1-5": {
                "name": "Stable Diffusion v1.5",
                "description": "经典的图像生成模型",
                "type": "diffusion",
                "class": "StableDiffusionModel",
                "model_id": "runwayml/stable-diffusion-v1-5",
                "supported_sizes": [(512, 512), (768, 512), (512, 768)],
                "max_batch_size": 8,
                "memory_requirement": 4.0,  # GB
                "precision": "fp16"
            }
        }
        
        # 加载自定义模型配置
        if self.model_configs_path.exists():
            for config_file in self.model_configs_path.glob("*.yaml"):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        custom_config = yaml.safe_load(f)
                    
                    model_id = config_file.stem
                    self.available_models[model_id] = custom_config
                    logger.info(f"加载自定义模型配置: {model_id}")
                    
                except Exception as e:
                    logger.error(f"加载模型配置失败 {config_file}: {e}")
    
    async def load_model(self, model_id: str) -> bool:
        """加载指定模型"""
        if model_id in self.loaded_models:
            logger.info(f"模型 {model_id} 已加载")
            return True
        
        if model_id not in self.available_models:
            raise ValueError(f"未知模型: {model_id}")
        
        # 检查内存是否足够
        if not await self._check_memory_availability(model_id):
            # 尝试卸载一些模型释放内存
            await self._free_memory_for_model(model_id)
        
        config = self.available_models[model_id]
        
        try:
            logger.info(f"开始加载模型: {model_id}")
            
            # 根据模型类型创建实例
            model_class = config["class"]
            if model_class == "StableDiffusionModel":
                model = StableDiffusionModel(config, self.model_cache_dir)
            elif model_class == "FluxModel":
                model = FluxModel(config, self.model_cache_dir)
            else:
                raise ValueError(f"不支持的模型类型: {model_class}")
            
            # 加载模型
            await model.load()
            
            # 添加到已加载模型列表
            self.loaded_models[model_id] = model
            
            logger.info(f"模型加载成功: {model_id}")
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败 {model_id}: {e}")
            raise
    
    async def unload_model(self, model_id: str) -> bool:
        """卸载指定模型"""
        if model_id not in self.loaded_models:
            logger.warning(f"模型 {model_id} 未加载")
            return False
        
        try:
            logger.info(f"开始卸载模型: {model_id}")
            
            # 获取模型实例
            model = self.loaded_models[model_id]
            
            # 卸载模型
            await model.unload()
            
            # 从已加载列表中移除
            del self.loaded_models[model_id]
            
            # 强制垃圾回收
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            logger.info(f"模型卸载成功: {model_id}")
            return True
            
        except Exception as e:
            logger.error(f"模型卸载失败 {model_id}: {e}")
            return False
    
    async def get_model(self, model_id: str):
        """获取模型实例"""
        if model_id not in self.loaded_models:
            # 尝试自动加载
            await self.load_model(model_id)
        
        return self.loaded_models.get(model_id)
    
    async def _check_memory_availability(self, model_id: str) -> bool:
        """检查内存是否足够加载模型"""
        if not torch.cuda.is_available():
            return True  # CPU模式，假设内存足够
        
        config = self.available_models[model_id]
        required_memory = config.get("memory_requirement", 4.0)  # GB
        
        # 获取当前GPU内存使用情况
        current_memory = torch.cuda.memory_allocated() / 1024**3  # GB
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        available_memory = total_memory - current_memory
        
        # 保留一些缓冲内存
        buffer_memory = 1.0  # GB
        
        return available_memory >= (required_memory + buffer_memory)
    
    async def _free_memory_for_model(self, model_id: str):
        """为新模型释放内存"""
        config = self.available_models[model_id]
        required_memory = config.get("memory_requirement", 4.0)
        
        # 按照最近最少使用原则卸载模型
        models_to_unload = []
        freed_memory = 0.0
        
        # 简单策略：卸载内存占用最大的模型
        for loaded_id, model in self.loaded_models.items():
            model_config = self.available_models[loaded_id]
            model_memory = model_config.get("memory_requirement", 4.0)
            
            models_to_unload.append((loaded_id, model_memory))
            freed_memory += model_memory
            
            if freed_memory >= required_memory:
                break
        
        # 执行卸载
        for model_id_to_unload, _ in models_to_unload:
            await self.unload_model(model_id_to_unload)
            logger.info(f"为加载新模型卸载了: {model_id_to_unload}")
    
    async def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        memory_info = {}
        
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                memory_info[f"gpu_{i}"] = {
                    "allocated": torch.cuda.memory_allocated(i) / 1024**3,
                    "reserved": torch.cuda.memory_reserved(i) / 1024**3,
                    "total": torch.cuda.get_device_properties(i).total_memory / 1024**3
                }
        
        # 模型内存使用
        model_memory = {}
        for model_id, model in self.loaded_models.items():
            if hasattr(model, 'get_memory_usage'):
                model_memory[model_id] = model.get_memory_usage()
            else:
                config = self.available_models[model_id]
                model_memory[model_id] = config.get("memory_requirement", 0.0)
        
        memory_info["models"] = model_memory
        return memory_info
    
    async def optimize_memory(self):
        """优化内存使用"""
        if torch.cuda.is_available():
            # 清理GPU缓存
            torch.cuda.empty_cache()
        
        # 强制垃圾回收
        gc.collect()
        
        logger.info("内存优化完成")
    
    async def get_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        if model_id not in self.available_models:
            return None
        
        config = self.available_models[model_id].copy()
        config["loaded"] = model_id in self.loaded_models
        
        if config["loaded"]:
            model = self.loaded_models[model_id]
            if hasattr(model, 'get_info'):
                config.update(model.get_info())
        
        return config
    
    async def list_loaded_models(self) -> List[str]:
        """获取已加载模型列表"""
        return list(self.loaded_models.keys())
    
    async def list_available_models(self) -> List[str]:
        """获取可用模型列表"""
        return list(self.available_models.keys())
    
    async def reload_model(self, model_id: str) -> bool:
        """重新加载模型"""
        if model_id in self.loaded_models:
            await self.unload_model(model_id)
        
        return await self.load_model(model_id)
    
    async def cleanup(self):
        """清理所有模型"""
        logger.info("开始清理所有模型...")
        
        # 卸载所有已加载的模型
        for model_id in list(self.loaded_models.keys()):
            await self.unload_model(model_id)
        
        # 最终内存清理
        await self.optimize_memory()
        
        logger.info("模型清理完成")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "loaded_models": len(self.loaded_models),
            "available_models": len(self.available_models),
            "loaded_model_list": list(self.loaded_models.keys()),
            "available_model_list": list(self.available_models.keys()),
            "max_loaded_models": self.max_loaded_models
        }
