# Prometheus告警规则配置

groups:
  # 系统资源告警
  - name: system_alerts
    rules:
      # CPU使用率过高
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "实例 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "实例 {{ $labels.instance }} 内存使用率超过85%，当前值: {{ $value }}%"

      # 磁盘空间不足
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "实例 {{ $labels.instance }} 磁盘使用率超过90%，当前值: {{ $value }}%"

  # 应用服务告警
  - name: application_alerts
    rules:
      # 服务不可用
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务不可用"
          description: "服务 {{ $labels.job }} 实例 {{ $labels.instance }} 已停止响应"

      # HTTP错误率过高
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "HTTP错误率过高"
          description: "服务 {{ $labels.job }} 5xx错误率超过5%，当前值: {{ $value }}%"

      # 响应时间过长
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过长"
          description: "服务 {{ $labels.job }} 95%响应时间超过2秒，当前值: {{ $value }}秒"

  # GPU和算力节点告警
  - name: compute_alerts
    rules:
      # GPU使用率过高
      - alert: HighGPUUsage
        expr: nvidia_gpu_utilization > 95
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "GPU使用率过高"
          description: "节点 {{ $labels.instance }} GPU使用率超过95%，当前值: {{ $value }}%"

      # GPU温度过高
      - alert: HighGPUTemperature
        expr: nvidia_gpu_temperature > 85
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "GPU温度过高"
          description: "节点 {{ $labels.instance }} GPU温度超过85°C，当前值: {{ $value }}°C"

      # 算力节点离线
      - alert: ComputeNodeOffline
        expr: compute_node_status != 1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "算力节点离线"
          description: "算力节点 {{ $labels.node_id }} 已离线"

      # 任务队列积压
      - alert: TaskQueueBacklog
        expr: task_queue_size > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "任务队列积压"
          description: "任务队列积压超过100个任务，当前值: {{ $value }}"

  # 数据库告警
  - name: database_alerts
    rules:
      # PostgreSQL连接数过多
      - alert: PostgreSQLTooManyConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL连接数过多"
          description: "PostgreSQL连接数超过80%，当前值: {{ $value }}%"

      # Redis内存使用率过高
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过90%，当前值: {{ $value }}%"

  # 业务指标告警
  - name: business_alerts
    rules:
      # 图像生成失败率过高
      - alert: HighImageGenerationFailureRate
        expr: rate(image_generation_failures_total[5m]) / rate(image_generation_total[5m]) * 100 > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "图像生成失败率过高"
          description: "图像生成失败率超过10%，当前值: {{ $value }}%"

      # 用户请求量异常
      - alert: UnusualUserRequestVolume
        expr: rate(user_requests_total[5m]) > 1000 or rate(user_requests_total[5m]) < 10
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "用户请求量异常"
          description: "用户请求量异常，当前值: {{ $value }} req/s"

      # 平均任务处理时间过长
      - alert: LongAverageTaskProcessingTime
        expr: avg(task_processing_duration_seconds) > 300
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "平均任务处理时间过长"
          description: "平均任务处理时间超过5分钟，当前值: {{ $value }}秒"
