@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ComfyUI云端算力启动脚本 (Windows版本)

echo 🚀 ComfyUI云端算力启动脚本
echo ================================

:: 检查Python
echo [INFO] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装，请先安装Python
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [SUCCESS] Python版本: %PYTHON_VERSION%

:: 检查参数
if "%1"=="install" goto install
if "%1"=="test" goto test
if "%1"=="config" goto config
if "%1"=="start" goto start
if "%1"=="help" goto help
if "%1"=="" goto help

echo [ERROR] 未知命令: %1
goto help

:install
echo [INFO] 安装Python依赖...
cd compute-node

:: 检查requirements.txt是否存在
if not exist requirements.txt (
    echo [INFO] 创建requirements.txt文件...
    (
        echo aiohttp^>=3.9.0
        echo websockets^>=12.0
        echo PyYAML^>=6.0.1
        echo asyncio
    ) > requirements.txt
)

:: 安装依赖
pip install -r requirements.txt
if errorlevel 1 (
    echo [ERROR] 依赖安装失败
    cd ..
    pause
    exit /b 1
)

echo [SUCCESS] 依赖安装完成
cd ..
echo [INFO] 接下来运行: %0 test
pause
goto end

:test
echo [INFO] 测试ComfyUI连接...
cd compute-node
python test_comfyui.py
cd ..
pause
goto end

:config
echo [INFO] 配置ComfyUI节点...
cd compute-node

if not exist comfyui_config.yaml (
    echo [ERROR] 配置文件不存在，请先运行脚本生成配置文件
    cd ..
    pause
    exit /b 1
)

echo [INFO] 当前配置:
echo   ComfyUI服务: https://koomfonjr8-8188.cnb.run
echo   调度中心: http://localhost:8001
echo   最大并发: 2个任务

set /p modify_config="是否修改配置？(y/n): "
if /i "%modify_config%"=="y" (
    echo [INFO] 请手动编辑 compute-node/comfyui_config.yaml 文件
    notepad comfyui_config.yaml
)

cd ..
pause
goto end

:start
echo [INFO] 启动ComfyUI算力节点...
cd compute-node

:: 创建日志目录
if not exist logs mkdir logs

echo [SUCCESS] 节点启动中...
echo [INFO] 按 Ctrl+C 停止节点
echo.

python start_comfyui_node.py

cd ..
pause
goto end

:help
echo ComfyUI云端算力启动脚本
echo.
echo 用法: %0 [命令]
echo.
echo 命令:
echo   install     安装依赖
echo   test        测试连接
echo   config      配置节点
echo   start       启动节点
echo   help        显示帮助
echo.
echo 快速启动:
echo   %0 install
echo   %0 test
echo   %0 start
echo.
pause
goto end

:end
