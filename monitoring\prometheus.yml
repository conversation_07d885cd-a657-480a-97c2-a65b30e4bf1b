# Prometheus配置文件
global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 后端API服务监控
  - job_name: 'dreamify-backend'
    static_configs:
      - targets: ['backend:5000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # AI服务监控
  - job_name: 'dreamify-ai-service'
    static_configs:
      - targets: ['ai-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # 调度器监控
  - job_name: 'dreamify-scheduler'
    static_configs:
      - targets: ['scheduler:8001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # 算力节点监控
  - job_name: 'compute-nodes'
    static_configs:
      - targets: []  # 动态发现算力节点
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  # MinIO监控
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: '/minio/v2/metrics/cluster'

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/nginx_status'

  # Node Exporter (系统监控)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # cAdvisor (容器监控)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

# 远程写入配置（可选，用于长期存储）
# remote_write:
#   - url: "http://remote-storage:9201/write"

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"
