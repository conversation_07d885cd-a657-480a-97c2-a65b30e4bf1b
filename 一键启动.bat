@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █    🚀 ComfyUI云端算力节点 - 一键启动脚本                      █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

:: 检查Python
echo [1/5] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装，请先安装Python
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

:: 进入正确目录
echo.
echo [2/5] 进入项目目录...
cd /d "%~dp0"
if not exist "compute-node" (
    echo ❌ 找不到compute-node目录
    echo 💡 请确保脚本在项目根目录运行
    pause
    exit /b 1
)
cd compute-node
echo ✅ 当前目录: %CD%

:: 安装依赖
echo.
echo [3/5] 安装Python依赖包...
echo 📦 正在安装: aiohttp websockets PyYAML...
pip install aiohttp websockets PyYAML >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 依赖安装可能有问题，但继续尝试...
) else (
    echo ✅ 依赖安装完成
)

:: 测试连接
echo.
echo [4/5] 测试ComfyUI连接...
python simple_test.py
if errorlevel 1 (
    echo ❌ 连接测试失败
    echo 💡 请检查网络连接和ComfyUI服务状态
    pause
    exit /b 1
)

:: 启动选择菜单
echo.
echo [5/5] 选择启动模式...
echo.
echo ┌─────────────────────────────────────────┐
echo │              启动选项菜单                │
echo ├─────────────────────────────────────────┤
echo │  1. 🌐 启动Web管理界面 (推荐)            │
echo │  2. 🔍 运行详细诊断测试                  │
echo │  3. 🎨 测试图像生成功能                  │
echo │  4. 🚀 直接启动算力节点                  │
echo │  5. 🌍 打开ComfyUI网页界面               │
echo │  0. ❌ 退出                             │
echo └─────────────────────────────────────────┘
echo.

:menu
set /p choice="请选择 (0-5): "

if "%choice%"=="1" goto start_web
if "%choice%"=="2" goto run_diagnosis
if "%choice%"=="3" goto test_generation
if "%choice%"=="4" goto start_node
if "%choice%"=="5" goto open_comfyui
if "%choice%"=="0" goto exit
echo ❌ 无效选择，请重新输入
goto menu

:start_web
echo.
echo 🌐 启动Web管理界面...
echo ┌─────────────────────────────────────────┐
echo │  Web界面将在浏览器中自动打开             │
echo │  地址: http://localhost:8080            │
echo │  按 Ctrl+C 停止服务                     │
echo └─────────────────────────────────────────┘
echo.
timeout /t 3 /nobreak >nul
start http://localhost:8080
python web_interface.py
goto end

:run_diagnosis
echo.
echo 🔍 运行详细诊断...
python check_comfyui_service.py
echo.
echo 📋 诊断完成，按任意键返回菜单...
pause >nul
goto menu

:test_generation
echo.
echo 🎨 测试图像生成功能...
python test_with_default_model.py
echo.
echo 📋 测试完成，按任意键返回菜单...
pause >nul
goto menu

:start_node
echo.
echo 🚀 启动算力节点...
echo ┌─────────────────────────────────────────┐
echo │  节点将连接到调度中心                    │
echo │  按 Ctrl+C 停止节点                     │
echo └─────────────────────────────────────────┘
echo.
python start_comfyui_node.py
goto end

:open_comfyui
echo.
echo 🌍 打开ComfyUI网页界面...
start https://koomfonjr8-8188.cnb.run
echo ✅ ComfyUI界面已在浏览器中打开
echo.
echo 📋 按任意键返回菜单...
pause >nul
goto menu

:exit
echo.
echo 👋 感谢使用！
goto end

:end
echo.
echo 📝 使用说明:
echo   • Web界面: 最简单的管理方式
echo   • 诊断测试: 排查连接问题
echo   • 生成测试: 验证AI功能
echo   • 直接启动: 连接调度中心
echo.
pause
