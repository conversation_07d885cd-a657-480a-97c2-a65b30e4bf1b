#!/usr/bin/env python3
"""
使用默认模型测试ComfyUI生成
即使检测不到模型列表，也尝试使用常见的模型名称
"""

import asyncio
import sys
from comfyui_client import ComfyUIClient

# 常见的ComfyUI模型名称
COMMON_MODELS = [
    "sd_xl_base_1.0.safetensors",
    "v1-5-pruned-emaonly.ckpt", 
    "v1-5-pruned.ckpt",
    "sd-v1-4.ckpt",
    "anything-v4.5-pruned.ckpt",
    "dreamshaper_8.safetensors",
    "realisticVisionV60B1_v60B1VAE.safetensors"
]

async def test_with_default_models():
    """使用默认模型名称测试生成"""
    print("🎨 使用默认模型测试图像生成...")
    print("=" * 50)
    
    client = ComfyUIClient("https://koomfonjr8-8188.cnb.run")
    
    try:
        # 连接服务
        await client.connect()
        print("✅ 连接成功")
        
        # 尝试每个常见模型
        for model_name in COMMON_MODELS:
            print(f"\n🔄 尝试模型: {model_name}")
            
            try:
                # 创建简单的工作流
                workflow = client.create_workflow(
                    prompt="a cute cat, highly detailed",
                    negative_prompt="blurry, low quality",
                    width=512,
                    height=512,
                    steps=10,  # 使用很少的步数快速测试
                    cfg_scale=7.5,
                    seed=12345,
                    checkpoint=model_name
                )
                
                # 提交工作流
                prompt_id = await client.queue_prompt(workflow)
                print(f"   ✅ 工作流提交成功，ID: {prompt_id}")
                
                # 等待完成（短时间）
                try:
                    result = await asyncio.wait_for(
                        client.wait_for_completion(prompt_id, timeout=60),
                        timeout=60
                    )
                    
                    if result and result.get('images'):
                        print(f"   🎉 生成成功！共 {len(result['images'])} 张图片")
                        
                        # 保存第一张图片
                        import base64
                        image_data = base64.b64decode(result['images'][0]['data'])
                        filename = f"test_{model_name.replace('.', '_')}.png"
                        with open(filename, "wb") as f:
                            f.write(image_data)
                        print(f"   💾 图片已保存: {filename}")
                        
                        print(f"\n🎉 找到可用模型: {model_name}")
                        return model_name
                    else:
                        print("   ❌ 生成失败：没有返回图片")
                        
                except asyncio.TimeoutError:
                    print("   ⏰ 生成超时")
                except Exception as e:
                    print(f"   ❌ 生成过程出错: {e}")
                    
            except Exception as e:
                print(f"   ❌ 模型 {model_name} 不可用: {e}")
                continue
        
        print("\n❌ 所有常见模型都不可用")
        return None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None
    finally:
        await client.disconnect()

async def main():
    """主函数"""
    print("🚀 ComfyUI默认模型测试")
    print("=" * 50)
    
    # 先运行详细检查
    print("1️⃣ 运行详细服务检查...")
    try:
        from check_comfyui_service import check_comfyui_service
        await check_comfyui_service()
    except ImportError:
        print("   ⚠️ 详细检查脚本不可用，跳过")
    
    print("\n" + "=" * 50)
    print("2️⃣ 尝试使用常见模型名称...")
    
    # 测试默认模型
    working_model = await test_with_default_models()
    
    if working_model:
        print(f"\n🎉 成功！可用模型: {working_model}")
        print("\n📝 接下来您可以:")
        print(f"1. 在配置文件中使用模型: {working_model}")
        print("2. 启动算力节点连接到调度中心")
        print("3. 开始处理真实的图像生成任务")
    else:
        print("\n😔 没有找到可用的模型")
        print("\n💡 建议:")
        print("1. 联系ComfyUI服务提供者确认模型状态")
        print("2. 等待服务完全启动后重试")
        print("3. 检查是否需要手动上传模型文件")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
