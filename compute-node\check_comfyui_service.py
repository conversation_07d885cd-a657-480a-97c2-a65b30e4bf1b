#!/usr/bin/env python3
"""
ComfyUI服务详细检查脚本
用于诊断ComfyUI服务状态和可用模型
"""

import asyncio
import aiohttp
import json
import sys

async def check_comfyui_service(server_url="https://koomfonjr8-8188.cnb.run"):
    """详细检查ComfyUI服务"""
    
    print(f"🔍 检查ComfyUI服务: {server_url}")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 基本连接测试
        print("1️⃣ 基本连接测试...")
        try:
            async with session.get(server_url) as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    content = await response.text()
                    print(f"   响应长度: {len(content)} 字符")
                    if "ComfyUI" in content:
                        print("   ✅ 确认是ComfyUI服务")
                    else:
                        print("   ⚠️ 可能不是ComfyUI服务")
                else:
                    print(f"   ❌ 连接失败: {response.status}")
                    return
        except Exception as e:
            print(f"   ❌ 连接异常: {e}")
            return
        
        # 2. 检查object_info API
        print("\n2️⃣ 检查object_info API...")
        try:
            async with session.get(f"{server_url}/object_info") as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ API正常，包含 {len(data)} 个节点类型")
                    
                    # 查找关键节点
                    key_nodes = [
                        "CheckpointLoaderSimple",
                        "VAELoader", 
                        "CLIPTextEncode",
                        "KSampler",
                        "SaveImage"
                    ]
                    
                    print("   🔧 关键节点检查:")
                    for node in key_nodes:
                        if node in data:
                            print(f"      ✅ {node}")
                        else:
                            print(f"      ❌ {node} (缺失)")
                    
                    # 详细检查CheckpointLoaderSimple
                    if "CheckpointLoaderSimple" in data:
                        print("\n   📦 CheckpointLoaderSimple详细信息:")
                        loader = data["CheckpointLoaderSimple"]
                        print(f"      类型: {loader.get('category', 'unknown')}")
                        
                        if "input" in loader:
                            inputs = loader["input"]
                            print(f"      输入参数: {list(inputs.keys())}")
                            
                            if "ckpt_name" in inputs:
                                ckpt_info = inputs["ckpt_name"]
                                print(f"      ckpt_name类型: {type(ckpt_info)}")
                                print(f"      ckpt_name内容: {ckpt_info}")
                                
                                # 尝试解析模型列表
                                models = []
                                if isinstance(ckpt_info, list):
                                    if len(ckpt_info) > 0:
                                        if isinstance(ckpt_info[0], list):
                                            models = ckpt_info[0]
                                        else:
                                            models = ckpt_info
                                
                                print(f"      📋 解析到的模型数量: {len(models)}")
                                if models:
                                    print("      📋 可用模型:")
                                    for i, model in enumerate(models[:10]):  # 只显示前10个
                                        print(f"         {i+1}. {model}")
                                    if len(models) > 10:
                                        print(f"         ... 还有 {len(models) - 10} 个模型")
                                else:
                                    print("      ⚠️ 没有检测到任何模型")
                            else:
                                print("      ❌ 没有ckpt_name参数")
                        else:
                            print("      ❌ 没有input信息")
                    
                else:
                    error_text = await response.text()
                    print(f"   ❌ API调用失败: {error_text}")
                    
        except Exception as e:
            print(f"   ❌ API异常: {e}")
        
        # 3. 检查队列状态
        print("\n3️⃣ 检查队列状态...")
        try:
            async with session.get(f"{server_url}/queue") as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    queue_data = await response.json()
                    print(f"   ✅ 队列API正常")
                    print(f"   📊 队列信息: {queue_data}")
                else:
                    print(f"   ❌ 队列API失败: {response.status}")
        except Exception as e:
            print(f"   ❌ 队列API异常: {e}")
        
        # 4. 检查历史记录
        print("\n4️⃣ 检查历史记录...")
        try:
            async with session.get(f"{server_url}/history") as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    print("   ✅ 历史记录API正常")
                else:
                    print(f"   ❌ 历史记录API失败: {response.status}")
        except Exception as e:
            print(f"   ❌ 历史记录API异常: {e}")
        
        # 5. 尝试WebSocket连接
        print("\n5️⃣ 检查WebSocket连接...")
        try:
            import websockets
            ws_url = server_url.replace('https://', 'wss://').replace('http://', 'ws://')
            ws_url += "/ws?clientId=test"
            
            async with websockets.connect(ws_url) as websocket:
                print("   ✅ WebSocket连接成功")
                
                # 发送测试消息
                await websocket.send('{"type": "test"}')
                
                # 等待响应（超时1秒）
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    print(f"   📨 收到响应: {response}")
                except asyncio.TimeoutError:
                    print("   ⏰ WebSocket响应超时（正常）")
                    
        except Exception as e:
            print(f"   ❌ WebSocket连接失败: {e}")
        
        # 6. 总结和建议
        print("\n" + "=" * 60)
        print("📋 诊断总结:")
        print("1. 如果没有检测到模型，可能的原因:")
        print("   - ComfyUI服务刚启动，模型还在加载中")
        print("   - 模型文件没有放在正确的目录")
        print("   - ComfyUI配置有问题")
        print("\n2. 建议的解决方案:")
        print("   - 等待几分钟让ComfyUI完全启动")
        print("   - 检查ComfyUI的models/checkpoints目录")
        print("   - 重启ComfyUI服务")
        print("   - 手动上传一些模型文件")

async def main():
    """主函数"""
    server_url = "https://koomfonjr8-8188.cnb.run"
    
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    
    await check_comfyui_service(server_url)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 检查被用户中断")
    except Exception as e:
        print(f"\n❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
