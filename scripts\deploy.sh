#!/bin/bash

# Dreamify AI绘画平台部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_warning "Node.js未安装，将使用Docker容器运行"
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_warning "Python3未安装，将使用Docker容器运行"
    fi
    
    log_success "依赖检查完成"
}

# 环境配置
setup_environment() {
    local env_type=$1
    log_info "配置${env_type}环境..."
    
    # 复制环境变量文件
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            log_info "已创建.env文件，请根据需要修改配置"
        else
            log_error ".env.example文件不存在"
            exit 1
        fi
    fi
    
    # 创建必要的目录
    mkdir -p logs
    mkdir -p models
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p data/minio
    mkdir -p ssl
    
    log_success "环境配置完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker build -t dreamify-frontend:latest ./frontend
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build -t dreamify-backend:latest ./backend
    
    # 构建AI服务镜像
    log_info "构建AI服务镜像..."
    docker build -t dreamify-ai-service:latest ./ai-service
    
    # 构建调度器镜像
    log_info "构建调度器镜像..."
    docker build -t dreamify-scheduler:latest ./scheduler
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    local env_type=$1
    log_info "启动${env_type}环境服务..."
    
    if [ "$env_type" = "development" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    else
        docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    fi
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待数据库
    log_info "等待PostgreSQL启动..."
    until docker-compose exec postgres pg_isready -U dreamify; do
        sleep 2
    done
    
    # 等待Redis
    log_info "等待Redis启动..."
    until docker-compose exec redis redis-cli ping; do
        sleep 2
    done
    
    # 等待后端API
    log_info "等待后端API启动..."
    until curl -f http://localhost:5000/health; do
        sleep 5
    done
    
    log_success "所有服务已就绪"
}

# 数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    docker-compose exec backend npm run migrate
    
    log_success "数据库迁移完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查前端
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务正常"
    else
        log_error "前端服务异常"
    fi
    
    # 检查后端API
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        log_success "后端API服务正常"
    else
        log_error "后端API服务异常"
    fi
    
    # 检查AI服务
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "AI服务正常"
    else
        log_error "AI服务异常"
    fi
    
    # 检查调度器
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        log_success "调度器服务正常"
    else
        log_error "调度器服务异常"
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的卷
    docker volume prune -f
    
    log_success "资源清理完成"
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    log_info "备份数据到 $backup_dir..."
    
    # 备份数据库
    docker-compose exec postgres pg_dump -U dreamify dreamify > "$backup_dir/database.sql"
    
    # 备份文件存储
    cp -r data/minio "$backup_dir/"
    
    # 备份配置文件
    cp .env "$backup_dir/"
    
    log_success "数据备份完成"
}

# 恢复数据
restore_data() {
    local backup_dir=$1
    
    if [ -z "$backup_dir" ]; then
        log_error "请指定备份目录"
        exit 1
    fi
    
    if [ ! -d "$backup_dir" ]; then
        log_error "备份目录不存在: $backup_dir"
        exit 1
    fi
    
    log_info "从 $backup_dir 恢复数据..."
    
    # 恢复数据库
    if [ -f "$backup_dir/database.sql" ]; then
        docker-compose exec -T postgres psql -U dreamify dreamify < "$backup_dir/database.sql"
        log_success "数据库恢复完成"
    fi
    
    # 恢复文件存储
    if [ -d "$backup_dir/minio" ]; then
        cp -r "$backup_dir/minio"/* data/minio/
        log_success "文件存储恢复完成"
    fi
    
    log_success "数据恢复完成"
}

# 显示帮助信息
show_help() {
    echo "Dreamify AI绘画平台部署脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  dev         启动开发环境"
    echo "  prod        启动生产环境"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  build       构建镜像"
    echo "  health      健康检查"
    echo "  logs        查看日志"
    echo "  backup      备份数据"
    echo "  restore     恢复数据"
    echo "  cleanup     清理资源"
    echo "  help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev              # 启动开发环境"
    echo "  $0 prod             # 启动生产环境"
    echo "  $0 backup           # 备份数据"
    echo "  $0 restore backup_dir # 恢复数据"
}

# 主函数
main() {
    local command=$1
    local option=$2
    
    case $command in
        "dev")
            check_dependencies
            setup_environment "development"
            build_images
            start_services "development"
            wait_for_services
            run_migrations
            health_check
            log_success "开发环境部署完成！"
            log_info "前端地址: http://localhost:3000"
            log_info "后端API: http://localhost:5000"
            log_info "监控面板: http://localhost:3001"
            ;;
        "prod")
            check_dependencies
            setup_environment "production"
            build_images
            start_services "production"
            wait_for_services
            run_migrations
            health_check
            log_success "生产环境部署完成！"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            start_services "development"
            ;;
        "build")
            build_images
            ;;
        "health")
            health_check
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data "$option"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
