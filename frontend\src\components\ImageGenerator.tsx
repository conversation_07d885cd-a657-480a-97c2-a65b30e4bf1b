'use client';

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Sparkles, 
  Upload, 
  Settings, 
  ChevronDown, 
  ChevronUp,
  Wand2,
  Image as ImageIcon,
  Loader2
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';

import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Select } from '@/components/ui/Select';
import { Slider } from '@/components/ui/Slider';
import { Switch } from '@/components/ui/Switch';
import { Progress } from '@/components/ui/Progress';
import { useTaskStore } from '@/store/taskStore';
import { useAuthStore } from '@/store/authStore';
import { apiClient } from '@/lib/api';

// 表单验证模式
const generateImageSchema = z.object({
  prompt: z.string().min(1, '请输入提示词').max(1000, '提示词不能超过1000字符'),
  model: z.enum(['stable-diffusion-xl', 'flux-dev', 'custom-model']),
  width: z.number().min(64).max(1920),
  height: z.number().min(64).max(1920),
  steps: z.number().min(1).max(100),
  batch_size: z.number().min(1).max(4),
  cfg_scale: z.number().min(1).max(20).optional(),
  seed: z.number().min(-1).optional(),
  negative_prompt: z.string().max(500).optional()
});

type GenerateImageForm = z.infer<typeof generateImageSchema>;

const models = [
  {
    id: 'stable-diffusion-xl',
    name: 'Stable Diffusion XL',
    description: '高质量通用模型，适合各种风格',
    image: '/images/models/sdxl.jpg'
  },
  {
    id: 'flux-dev',
    name: 'FLUX Dev',
    description: '最新的高质量模型，细节丰富',
    image: '/images/models/flux.jpg'
  },
  {
    id: 'custom-model',
    name: '自定义模型',
    description: '社区训练的特殊风格模型',
    image: '/images/models/custom.jpg'
  }
];

const presetSizes = [
  { name: '正方形', width: 1024, height: 1024 },
  { name: '横屏', width: 1344, height: 768 },
  { name: '竖屏', width: 768, height: 1344 },
  { name: '宽屏', width: 1536, height: 640 }
];

export function ImageGenerator() {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [generationProgress, setGenerationProgress] = useState(0);
  
  const { addTask } = useTaskStore();
  const { user } = useAuthStore();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid }
  } = useForm<GenerateImageForm>({
    resolver: zodResolver(generateImageSchema),
    defaultValues: {
      prompt: '',
      model: 'stable-diffusion-xl',
      width: 1024,
      height: 1024,
      steps: 30,
      batch_size: 4,
      cfg_scale: 7.5,
      seed: -1,
      negative_prompt: ''
    }
  });

  const watchedValues = watch();

  // 文件上传处理
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB限制
        toast.error('文件大小不能超过10MB');
        return;
      }
      setUploadedImage(file);
      toast.success('参考图片上传成功');
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    maxFiles: 1
  });

  // 随机提示词生成
  const generateRandomPrompt = async () => {
    const prompts = [
      'A serene landscape with mountains and a lake at sunset',
      'A futuristic city with flying cars and neon lights',
      'A magical forest with glowing mushrooms and fairy lights',
      'A steampunk airship floating above clouds',
      'A cozy library with floating books and warm lighting'
    ];
    
    const randomPrompt = prompts[Math.floor(Math.random() * prompts.length)];
    setValue('prompt', randomPrompt);
    toast.success('随机提示词已生成');
  };

  // 预设尺寸设置
  const setPresetSize = (width: number, height: number) => {
    setValue('width', width);
    setValue('height', height);
  };

  // 表单提交
  const onSubmit = async (data: GenerateImageForm) => {
    if (!user) {
      toast.error('请先登录');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 1000);

      // 创建任务
      const response = await apiClient.post('/tasks', {
        ...data,
        referenceImage: uploadedImage ? await fileToBase64(uploadedImage) : undefined
      });

      const task = response.data.data;
      addTask(task);

      toast.success('任务创建成功，正在生成中...');
      
      // 清理进度
      clearInterval(progressInterval);
      setGenerationProgress(100);
      
      setTimeout(() => {
        setIsGenerating(false);
        setGenerationProgress(0);
      }, 1000);

    } catch (error: any) {
      console.error('Generation failed:', error);
      toast.error(error.response?.data?.message || '生成失败，请重试');
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  // 文件转Base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 lg:gap-12">
      
      {/* 左侧控制面板 */}
      <div className="order-1 lg:order-1 lg:col-span-2">
        <motion.div
          className="relative bg-gradient-to-br from-slate-800/95 to-slate-700/95 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-cyan-400/30 h-full flex flex-col"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-blue-400/10 rounded-3xl" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.2),rgba(255,255,255,0))]" />

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8 relative flex-grow flex flex-col">
            
            {/* 提示词输入 */}
            <div className="space-y-4">
              <label className="flex items-center text-sm font-medium text-cyan-50 mb-3">
                <Sparkles className="w-5 h-5 mr-2" />
                提示词
              </label>
              <div className="flex gap-4">
                <Textarea
                  {...register('prompt')}
                  placeholder="描述你想看到的内容（推荐英语，仅HiDream模型支持中文）..."
                  className="flex-grow h-32 bg-slate-700/50 backdrop-blur-sm border border-cyan-400/30 rounded-2xl focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400/50 resize-none text-cyan-50 placeholder-cyan-700"
                  error={errors.prompt?.message}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={generateRandomPrompt}
                  className="h-32 px-6 py-3 text-lg rounded-xl border border-cyan-400/50 text-cyan-200 hover:bg-cyan-400/10"
                >
                  随机提示词
                </Button>
              </div>
            </div>

            {/* 参考图片上传 */}
            <div className="space-y-4">
              <label className="flex items-center text-sm font-medium text-cyan-50 mb-3">
                <Upload className="w-5 h-5 mr-2" />
                上传参考图片（可选）
              </label>
              <div
                {...getRootProps()}
                className={`aspect-[4/3] rounded-2xl border-2 border-dashed transition-colors cursor-pointer ${
                  isDragActive 
                    ? 'border-cyan-400 bg-cyan-400/10' 
                    : 'border-cyan-400/30 bg-slate-700/50 hover:bg-slate-600/50'
                }`}
              >
                <input {...getInputProps()} />
                <div className="h-full flex flex-col items-center justify-center p-6">
                  {uploadedImage ? (
                    <div className="text-center">
                      <ImageIcon className="w-12 h-12 text-cyan-400 mb-4 mx-auto" />
                      <p className="text-cyan-200 font-medium">{uploadedImage.name}</p>
                      <p className="text-cyan-200/60 text-sm mt-2">
                        {(uploadedImage.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  ) : (
                    <>
                      <Upload className="w-12 h-12 text-cyan-400/50 mb-4" />
                      <p className="text-cyan-200/80 text-center px-4">
                        点击或拖拽图片到此处上传
                        <br />
                        <span className="text-sm text-cyan-200/60">
                          支持 JPG、PNG 格式，最大 10MB
                        </span>
                      </p>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* 高级选项切换 */}
            <div className="border-t border-cyan-400/30 pt-8">
              <button
                type="button"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center text-sm text-cyan-200 hover:text-cyan-50 transition-colors"
              >
                <Settings className="w-4 h-4 mr-2" />
                {showAdvanced ? '收起高级选项' : '展开高级选项'}
                {showAdvanced ? (
                  <ChevronUp className="ml-2 h-5 w-5" />
                ) : (
                  <ChevronDown className="ml-2 h-5 w-5" />
                )}
              </button>

              {/* 高级选项内容 */}
              <motion.div
                initial={false}
                animate={{ height: showAdvanced ? 'auto' : 0, opacity: showAdvanced ? 1 : 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="mt-6 space-y-6">
                  
                  {/* 尺寸设置 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-cyan-50 mb-3">
                        宽度
                      </label>
                      <Input
                        type="number"
                        {...register('width', { valueAsNumber: true })}
                        min={64}
                        max={1920}
                        step={8}
                        className="text-center"
                        error={errors.width?.message}
                      />
                      <p className="mt-2 text-sm text-cyan-200/80">
                        图片宽度（64-1920，8的倍数）
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-cyan-50 mb-3">
                        高度
                      </label>
                      <Input
                        type="number"
                        {...register('height', { valueAsNumber: true })}
                        min={64}
                        max={1920}
                        step={8}
                        className="text-center"
                        error={errors.height?.message}
                      />
                      <p className="mt-2 text-sm text-cyan-200/80">
                        图片高度（64-1920，8的倍数）
                      </p>
                    </div>
                  </div>

                  {/* 预设尺寸 */}
                  <div>
                    <label className="block text-sm font-medium text-cyan-50 mb-3">
                      预设尺寸
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {presetSizes.map((preset) => (
                        <Button
                          key={preset.name}
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setPresetSize(preset.width, preset.height)}
                          className={`${
                            watchedValues.width === preset.width && watchedValues.height === preset.height
                              ? 'bg-cyan-400/20 border-cyan-400'
                              : ''
                          }`}
                        >
                          {preset.name}
                          <span className="text-xs opacity-70 ml-1">
                            {preset.width}×{preset.height}
                          </span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* 模型选择 */}
                  <div>
                    <label className="block text-sm font-medium text-cyan-50 mb-3">
                      AI模型
                    </label>
                    <Select
                      value={watchedValues.model}
                      onValueChange={(value) => setValue('model', value as any)}
                    >
                      {models.map((model) => (
                        <option key={model.id} value={model.id}>
                          {model.name}
                        </option>
                      ))}
                    </Select>
                    <p className="mt-2 text-sm text-cyan-200/80">
                      选择要使用的AI模型
                    </p>
                  </div>

                  {/* 其他参数 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-cyan-50 mb-3">
                        步数: {watchedValues.steps}
                      </label>
                      <Slider
                        value={[watchedValues.steps]}
                        onValueChange={([value]) => setValue('steps', value)}
                        min={1}
                        max={100}
                        step={1}
                        className="w-full"
                      />
                      <p className="mt-2 text-sm text-cyan-200/80">
                        数值越高细节越丰富（1-100）
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-cyan-50 mb-3">
                        生成数量: {watchedValues.batch_size}
                      </label>
                      <Slider
                        value={[watchedValues.batch_size]}
                        onValueChange={([value]) => setValue('batch_size', value)}
                        min={1}
                        max={4}
                        step={1}
                        className="w-full"
                      />
                      <p className="mt-2 text-sm text-cyan-200/80">
                        一次生成的图片数量（1-4）
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-between items-center mt-auto">
              <Button
                type="submit"
                disabled={!isValid || isGenerating}
                className="px-8 py-3 text-lg rounded-xl bg-gradient-to-r from-cyan-400 to-blue-400 text-white hover:from-cyan-300 hover:to-blue-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Wand2 className="w-5 h-5 mr-2" />
                    开始生成
                  </>
                )}
              </Button>
            </div>

            {/* 生成进度 */}
            {isGenerating && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="mt-6"
              >
                <div className="flex justify-between items-center mb-2">
                  <span className="text-cyan-50">生成进度</span>
                  <span className="text-cyan-200">
                    预计耗时: {Math.round(watchedValues.steps * 2)} 秒
                  </span>
                </div>
                <Progress value={generationProgress} className="w-full h-2" />
                <div className="mt-2 text-sm text-cyan-200/80 text-right">
                  正在处理...
                </div>
              </motion.div>
            )}
          </form>
        </motion.div>
      </div>

      {/* 右侧预览区域 */}
      <div className="order-2 lg:order-2 lg:col-span-3">
        <motion.div
          className="bg-slate-700/80 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-xl sm:shadow-2xl p-4 sm:p-8 border border-cyan-400/30 h-full flex flex-col"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex justify-between items-center mb-8">
            <div className="flex items-center gap-4">
              <ImageIcon className="w-8 h-8 text-cyan-400" />
              <h2 className="text-3xl font-semibold text-cyan-100">预览</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-8 flex-grow">
            {Array.from({ length: watchedValues.batch_size }).map((_, index) => (
              <div
                key={index}
                className="aspect-square relative rounded-2xl overflow-hidden bg-slate-600/50 backdrop-blur-sm border border-cyan-400/30 transform hover:scale-[1.02] transition-transform duration-300"
              >
                {isGenerating ? (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <Loader2 className="w-8 h-8 text-cyan-400 animate-spin mx-auto mb-4" />
                      <p className="text-cyan-200 text-sm">生成中...</p>
                    </div>
                  </div>
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-cyan-200/60">
                      <ImageIcon className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p>等待生成</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
