{"version": "0.2.0", "configurations": [{"name": "🌐 启动Web管理界面", "type": "python", "request": "launch", "program": "${workspaceFolder}/compute-node/web_interface.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "🔍 测试ComfyUI连接", "type": "python", "request": "launch", "program": "${workspaceFolder}/compute-node/simple_test.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/compute-node"}, {"name": "🎨 测试图像生成", "type": "python", "request": "launch", "program": "${workspaceFolder}/compute-node/test_with_default_model.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/compute-node"}, {"name": "🚀 启动算力节点", "type": "python", "request": "launch", "program": "${workspaceFolder}/compute-node/start_comfyui_node.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/compute-node"}, {"name": "🔧 详细诊断", "type": "python", "request": "launch", "program": "${workspaceFolder}/compute-node/check_comfyui_service.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/compute-node"}]}