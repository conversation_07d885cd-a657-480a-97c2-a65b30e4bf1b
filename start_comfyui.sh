#!/bin/bash

# ComfyUI云端算力启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装，请先安装Python3"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    log_success "Python版本: $python_version"
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖..."
    
    cd compute-node
    
    # 检查是否存在requirements文件
    if [ ! -f "requirements.txt" ]; then
        log_info "创建requirements.txt文件..."
        cat > requirements.txt << EOF
aiohttp>=3.9.0
websockets>=12.0
PyYAML>=6.0.1
asyncio
EOF
    fi
    
    # 安装依赖
    pip3 install -r requirements.txt
    
    log_success "依赖安装完成"
    cd ..
}

# 测试连接
test_connection() {
    log_info "测试ComfyUI连接..."
    
    cd compute-node
    python3 test_comfyui.py
    cd ..
}

# 配置节点
configure_node() {
    log_info "配置ComfyUI节点..."
    
    cd compute-node
    
    # 检查配置文件是否存在
    if [ ! -f "comfyui_config.yaml" ]; then
        log_error "配置文件不存在，请先运行脚本生成配置文件"
        exit 1
    fi
    
    log_info "当前配置:"
    echo "  ComfyUI服务: https://koomfonjr8-8188.cnb.run"
    echo "  调度中心: http://localhost:8001"
    echo "  最大并发: 2个任务"
    
    read -p "是否修改配置？(y/n): " modify_config
    
    if [[ $modify_config =~ ^[Yy]$ ]]; then
        log_info "请手动编辑 compute-node/comfyui_config.yaml 文件"
        if command -v nano &> /dev/null; then
            nano comfyui_config.yaml
        elif command -v vim &> /dev/null; then
            vim comfyui_config.yaml
        else
            log_warning "请使用文本编辑器打开 comfyui_config.yaml 文件进行编辑"
        fi
    fi
    
    cd ..
}

# 启动节点
start_node() {
    log_info "启动ComfyUI算力节点..."
    
    cd compute-node
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动节点
    log_success "节点启动中..."
    log_info "按 Ctrl+C 停止节点"
    
    python3 start_comfyui_node.py
    
    cd ..
}

# 显示帮助
show_help() {
    echo "ComfyUI云端算力启动脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  install     安装依赖"
    echo "  test        测试连接"
    echo "  config      配置节点"
    echo "  start       启动节点"
    echo "  help        显示帮助"
    echo ""
    echo "快速启动:"
    echo "  $0 install && $0 test && $0 start"
}

# 主函数
main() {
    local command=$1
    
    echo "🚀 ComfyUI云端算力启动脚本"
    echo "================================"
    
    case $command in
        "install")
            check_python
            install_dependencies
            log_success "安装完成！接下来运行: $0 test"
            ;;
        "test")
            check_python
            test_connection
            ;;
        "config")
            configure_node
            ;;
        "start")
            check_python
            start_node
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
