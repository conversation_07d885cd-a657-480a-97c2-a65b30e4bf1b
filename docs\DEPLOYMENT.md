# Dreamify AI绘画平台部署指南

## 概述

本文档详细介绍了如何部署Dreamify AI绘画平台，包括开发环境和生产环境的部署步骤。

## 系统要求

### 硬件要求

**最低配置：**
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB 可用空间
- GPU: NVIDIA RTX 3060 或更高（用于AI模型推理）

**推荐配置：**
- CPU: 8核心或更多
- 内存: 16GB RAM 或更多
- 存储: 100GB SSD
- GPU: NVIDIA RTX 4090 或更高

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows 10+ / macOS 12+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **NVIDIA Docker**: 2.0+ (GPU支持)
- **Node.js**: 18+ (可选，用于本地开发)
- **Python**: 3.9+ (可选，用于本地开发)

## 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-repo/dreamify-ai.git
cd dreamify-ai
```

### 2. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量（根据实际情况修改）
nano .env
```

### 3. 一键部署

```bash
# 开发环境
./scripts/deploy.sh dev

# 生产环境
./scripts/deploy.sh prod
```

## 详细部署步骤

### 开发环境部署

#### 1. 准备工作

```bash
# 检查Docker安装
docker --version
docker-compose --version

# 检查NVIDIA Docker（如果使用GPU）
nvidia-docker --version
```

#### 2. 配置环境变量

编辑 `.env` 文件，配置以下关键参数：

```env
# 数据库配置
DATABASE_URL=postgresql://dreamify:password@localhost:5432/dreamify
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key

# MinIO配置
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# API配置
API_PORT=5000
NEXT_PUBLIC_API_URL=http://localhost:5000
```

#### 3. 启动服务

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 4. 初始化数据库

```bash
# 运行数据库迁移
docker-compose exec backend npm run migrate

# 创建初始数据（可选）
docker-compose exec backend npm run seed
```

#### 5. 验证部署

访问以下地址验证服务：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:5000
- **API文档**: http://localhost:5000/docs
- **监控面板**: http://localhost:3001 (admin/admin)
- **MinIO控制台**: http://localhost:9001 (minioadmin/minioadmin)

### 生产环境部署

#### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装NVIDIA Docker（GPU支持）
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

#### 2. 安全配置

```bash
# 创建专用用户
sudo useradd -m -s /bin/bash dreamify
sudo usermod -aG docker dreamify

# 配置防火墙
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

#### 3. SSL证书配置

```bash
# 使用Let's Encrypt获取SSL证书
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ./ssl/
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ./ssl/
```

#### 4. 生产环境配置

编辑生产环境配置文件：

```bash
# 创建生产环境配置
cp docker-compose.yml docker-compose.prod.yml
```

修改 `docker-compose.prod.yml`：

```yaml
version: '3.8'

services:
  nginx:
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf

  backend:
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://dreamify:${DB_PASSWORD}@postgres:5432/dreamify
    restart: unless-stopped

  frontend:
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://your-domain.com
    restart: unless-stopped
```

#### 5. 启动生产服务

```bash
# 启动生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 设置自动重启
sudo systemctl enable docker
```

## 算力节点部署

### 1. 节点服务器准备

```bash
# 安装Python依赖
pip install -r compute-node/requirements.txt

# 配置节点信息
cp compute-node/config.yaml.example compute-node/config.yaml
```

### 2. 节点配置

编辑 `compute-node/config.yaml`：

```yaml
node_id: "node-001"
node_name: "RTX4090-Node-1"
scheduler_url: "https://your-domain.com"
gpu_model: "RTX 4090"
gpu_memory: 24576
max_concurrent_tasks: 4
supported_models:
  - "stable-diffusion-xl"
  - "flux-dev"
```

### 3. 启动算力节点

```bash
# 启动节点客户端
cd compute-node
python client.py

# 或使用systemd服务
sudo cp compute-node.service /etc/systemd/system/
sudo systemctl enable compute-node
sudo systemctl start compute-node
```

## 监控和维护

### 1. 监控配置

访问监控面板：
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001

### 2. 日志管理

```bash
# 查看服务日志
docker-compose logs -f [service_name]

# 日志轮转配置
sudo nano /etc/logrotate.d/dreamify
```

### 3. 备份策略

```bash
# 自动备份脚本
./scripts/deploy.sh backup

# 恢复数据
./scripts/deploy.sh restore backup_directory
```

### 4. 性能优化

```bash
# 清理未使用的Docker资源
docker system prune -f

# 优化数据库
docker-compose exec postgres psql -U dreamify -c "VACUUM ANALYZE;"
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   sudo netstat -tulpn | grep :5000
   
   # 检查Docker日志
   docker-compose logs backend
   ```

2. **GPU不可用**
   ```bash
   # 检查NVIDIA驱动
   nvidia-smi
   
   # 检查Docker GPU支持
   docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec postgres pg_isready
   
   # 重置数据库
   docker-compose down -v
   docker-compose up -d postgres
   ```

### 性能调优

1. **数据库优化**
   - 调整PostgreSQL配置
   - 添加适当的索引
   - 定期执行VACUUM

2. **Redis优化**
   - 配置内存限制
   - 启用持久化
   - 监控内存使用

3. **GPU优化**
   - 调整批处理大小
   - 优化模型加载
   - 监控GPU温度

## 安全建议

1. **网络安全**
   - 使用防火墙限制访问
   - 配置SSL/TLS加密
   - 定期更新证书

2. **数据安全**
   - 定期备份数据
   - 加密敏感信息
   - 限制数据库访问

3. **应用安全**
   - 定期更新依赖
   - 使用强密码
   - 启用访问日志

## 扩展部署

### 水平扩展

1. **负载均衡**
   - 配置多个后端实例
   - 使用Nginx负载均衡
   - 实现会话粘性

2. **数据库集群**
   - 配置PostgreSQL主从复制
   - 使用Redis集群
   - 实现读写分离

3. **算力节点扩展**
   - 添加更多GPU节点
   - 实现自动发现
   - 负载均衡算法优化

### 云部署

1. **AWS部署**
   - 使用ECS/EKS
   - 配置RDS和ElastiCache
   - 使用S3存储

2. **Azure部署**
   - 使用AKS
   - 配置Azure Database
   - 使用Blob Storage

3. **GCP部署**
   - 使用GKE
   - 配置Cloud SQL
   - 使用Cloud Storage

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看[常见问题文档](FAQ.md)
2. 提交[GitHub Issue](https://github.com/your-repo/dreamify-ai/issues)
3. 联系技术支持: <EMAIL>
