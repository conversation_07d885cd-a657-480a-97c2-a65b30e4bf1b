/* 🎨 网络剪切板 - 现代化样式表 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主要背景渐变 */
    --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --bg-secondary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --bg-tertiary: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --bg-quaternary: linear-gradient(135deg, #10b981 0%, #059669 100%);
    
    /* 玻璃效果背景 */
    --bg-glass: rgba(255, 255, 255, 0.8);
    --bg-glass-hover: rgba(255, 255, 255, 0.9);
    --card-bg: rgba(255, 255, 255, 0.9);
    --card-bg-dark: rgba(30, 41, 59, 0.9);
    
    /* 文字颜色 */
    --text-color: #1e293b;
    --text-light: #ffffff;
    --text-medium: #475569;
    --text-dim: #64748b;
    
    /* 边框和高亮 */
    --border-color: rgba(59, 130, 246, 0.15);
    --card-border: rgba(148, 163, 184, 0.2);
    --highlight-color: #3b82f6;
    
    /* 按钮颜色 */
    --button-primary: #3b82f6;
    --button-danger: #ef4444;
    --button-success: #10b981;
    --button-info: #06b6d4;
    --button-warning: #f59e0b;
    --button-secondary: #6366f1;
    
    /* 阴影效果 */
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    --shadow-hover: 0 8px 20px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.2);
    
    /* 尺寸和间距 */
    --border-radius: 12px;
    --border-radius-small: 8px;
    --button-radius: 8px;
    --card-padding: 20px;
    
    /* 动画和效果 */
    --transition: all 0.3s ease;
    --backdrop-blur: blur(10px);
    --glass-border: 1px solid rgba(255, 255, 255, 0.3);
    --gradient-text: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* ===== 全局样式重置 ===== */
* {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", "Microsoft YaHei", Arial, sans-serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* ===== 主体样式 ===== */
body {
    background: var(--bg-primary);
    background-attachment: fixed;
    padding: 20px;
    margin: 0 auto;
    max-width: 1200px;
    color: var(--text-color);
    min-height: 100vh;
    font-size: 16px;
    line-height: 1.6;
    position: relative;
    overflow-x: hidden;
}

/* 背景装饰 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);
    z-index: -1;
}

/* ===== 标题样式 ===== */
h1 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--highlight-color);
    letter-spacing: -0.01em;
    position: relative;
    margin-top: 20px;
}

h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--bg-secondary);
    border-radius: 2px;
}

/* ===== 头部区域 ===== */
.header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    gap: 20px;
    position: relative;
    z-index: 10;
}

/* ===== 切换按钮样式 ===== */
.dark-mode-toggle {
    background: var(--bg-glass);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 56px;
    height: 56px;
    cursor: pointer;
    font-size: 24px;
    transition: var(--transition);
    box-shadow: var(--shadow);
    backdrop-filter: var(--backdrop-blur);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.dark-mode-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-secondary);
    opacity: 0;
    transition: var(--transition);
    border-radius: 50%;
}

.dark-mode-toggle:hover {
    transform: translateY(-4px) scale(1.1);
    box-shadow: var(--shadow-hover), var(--shadow-glow);
    border-color: var(--highlight-color);
}

.dark-mode-toggle:hover::before {
    opacity: 1;
}

.dark-mode-toggle:hover span {
    color: var(--text-light);
    z-index: 1;
    position: relative;
}

/* ===== 卡片样式 ===== */
.snippet {
    margin-bottom: 20px;
    padding: var(--card-padding);
    background: var(--card-bg);
    border: var(--glass-border);
    border-radius: var(--border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    transition: var(--transition);
    box-shadow: var(--shadow);
    font-size: 16px;
    line-height: 1.5;
    backdrop-filter: var(--backdrop-blur);
    position: relative;
    overflow: hidden;
    cursor: grab;
}

.snippet:hover {
    border-color: var(--highlight-color);
    box-shadow: var(--shadow-hover);
    background: var(--bg-glass-hover);
    transform: translateY(-1px);
}

/* ===== 拖拽样式 ===== */
.snippet.dragging {
    opacity: 0.5;
    transform: rotate(5deg) scale(1.05);
    cursor: grabbing;
    z-index: 1000;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.snippet.drag-over {
    border-color: var(--highlight-color);
    background: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
}

.drag-handle {
    cursor: grab;
    padding: 5px;
    margin-right: 10px;
    color: var(--text-dim);
    font-size: 18px;
    transition: var(--transition);
    border-radius: 4px;
}

.drag-handle:hover {
    color: var(--highlight-color);
    background: rgba(59, 130, 246, 0.1);
}

.drag-handle:active {
    cursor: grabbing;
}

/* ===== 内容区域 ===== */
.snippet-content {
    flex-grow: 1;
    text-align: left;
    padding: 10px;
    word-break: break-word;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    position: relative;
}

.snippet-content:hover {
    background: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.snippet-content:hover::after {
    content: "点击智能复制";
    position: absolute;
    top: 5px;
    right: 8px;
    font-size: 10px;
    color: var(--highlight-color);
    background: rgba(0, 123, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    opacity: 0.8;
    pointer-events: none;
    z-index: 10;
}

.snippet-content:active {
    transform: translateY(0);
    background: rgba(0, 123, 255, 0.1);
}

/* ===== 操作按钮区域 ===== */
.actions {
    display: flex;
    gap: 8px;
    align-items: center;
}
