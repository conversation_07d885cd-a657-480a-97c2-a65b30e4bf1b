# 🚀 ComfyUI云端算力启动指南

## 📋 启动前检查清单

- [ ] Python 3.7+ 已安装
- [ ] 网络连接正常
- [ ] ComfyUI服务地址可访问：https://koomfonjr8-8188.cnb.run
- [ ] 项目文件完整

## 🎯 快速启动（3步完成）

### 第1步：检查环境
```bash
# Windows
cd C:\Users\<USER>\Desktop\剪切板
start_comfyui.bat install

# Linux/Mac
cd ~/Desktop/剪切板
chmod +x start_comfyui.sh
./start_comfyui.sh install
```

### 第2步：测试连接
```bash
# Windows
start_comfyui.bat test

# Linux/Mac
./start_comfyui.sh test
```

### 第3步：启动节点
```bash
# Windows
start_comfyui.bat start

# Linux/Mac
./start_comfyui.sh start
```

## 🔧 详细启动步骤

### 1. 环境准备

#### 检查Python版本
```bash
python --version
# 或
python3 --version
```
需要Python 3.7或更高版本。

#### 安装依赖包
```bash
cd compute-node
pip install aiohttp websockets PyYAML
```

### 2. 服务诊断

#### 运行详细检查
```bash
cd compute-node
python check_comfyui_service.py
```

**预期输出：**
```
🔍 检查ComfyUI服务: https://koomfonjr8-8188.cnb.run
============================================================
1️⃣ 基本连接测试...
   状态码: 200
   响应长度: XXXX 字符
   ✅ 确认是ComfyUI服务

2️⃣ 检查object_info API...
   状态码: 200
   ✅ API正常，包含 XX 个节点类型
   🔧 关键节点检查:
      ✅ CheckpointLoaderSimple
      ✅ VAELoader
      ✅ CLIPTextEncode
      ✅ KSampler
      ✅ SaveImage
```

### 3. 模型测试

#### 测试可用模型
```bash
python test_with_default_model.py
```

**如果成功：**
```
🎉 找到可用模型: sd_xl_base_1.0.safetensors
💾 图片已保存: test_sd_xl_base_1_0_safetensors.png
```

### 4. 配置节点

#### 编辑配置文件
```bash
# 使用文本编辑器打开配置文件
notepad comfyui_config.yaml  # Windows
nano comfyui_config.yaml     # Linux/Mac
```

**关键配置项：**
```yaml
# ComfyUI服务配置
comfyui:
  server_url: "https://koomfonjr8-8188.cnb.run"

# 调度中心配置（如果有主服务）
scheduler:
  url: "http://localhost:8001"  # 修改为您的调度中心地址

# 性能配置
performance:
  max_concurrent_tasks: 2  # 根据服务性能调整
```

### 5. 启动算力节点

#### 启动命令
```bash
python start_comfyui_node.py
```

**成功启动的输出：**
```
INFO:ComputeNode-comfyui-node-001:初始化ComfyUI节点: ComfyUI云端节点
INFO:comfyui_client:成功连接到ComfyUI服务: https://koomfonjr8-8188.cnb.run
INFO:comfyui_client:WebSocket连接成功
INFO:ComputeNode-comfyui-node-001:可用模型: X 个检查点
INFO:ComputeNode-comfyui-node-001:节点注册成功
INFO:ComputeNode-comfyui-node-001:ComfyUI节点初始化完成
INFO:ComputeNode-comfyui-node-001:启动ComfyUI算力节点...
```

## 🎮 使用模式

### 模式1：独立使用（测试模式）
直接使用ComfyUI生成图像，不需要调度中心：
```bash
python test_comfyui.py
```

### 模式2：连接调度中心（生产模式）
连接到Dreamify主服务，作为算力节点：
```bash
python start_comfyui_node.py
```

### 模式3：API服务模式
将ComfyUI包装成REST API服务：
```bash
# 创建简单的API服务
python -c "
import asyncio
from comfyui_client import ComfyUIClient
from aiohttp import web

async def generate_image(request):
    data = await request.json()
    client = ComfyUIClient('https://koomfonjr8-8188.cnb.run')
    await client.connect()
    result = await client.generate_image(**data)
    await client.disconnect()
    return web.json_response(result)

app = web.Application()
app.router.add_post('/generate', generate_image)
web.run_app(app, host='0.0.0.0', port=8080)
"
```

## 🔍 状态监控

### 检查节点状态
```bash
# 查看日志
tail -f logs/comfyui_node.log

# 检查进程
ps aux | grep comfyui

# 测试API
curl http://localhost:8080/generate -X POST -H "Content-Type: application/json" -d '{"prompt":"a cat"}'
```

### 性能监控
- **CPU使用率**：通过系统监控工具
- **内存使用**：`htop` 或任务管理器
- **网络流量**：`iftop` 或网络监控
- **任务处理速度**：查看日志中的处理时间

## 🛠️ 故障排除

### 常见问题

#### 1. 连接失败
```
❌ 连接失败: Cannot connect to host
```
**解决方案：**
- 检查网络连接
- 确认ComfyUI服务地址正确
- 尝试在浏览器中访问服务地址

#### 2. 模型不可用
```
❌ 生成失败: 没有可用的检查点模型
```
**解决方案：**
- 等待ComfyUI服务完全启动
- 联系服务提供者确认模型状态
- 尝试使用不同的模型名称

#### 3. WebSocket连接失败
```
❌ WebSocket连接失败
```
**解决方案：**
- 检查防火墙设置
- 确认服务支持WebSocket
- 尝试使用HTTP轮询替代

#### 4. 生成超时
```
⏰ 生成超时
```
**解决方案：**
- 增加超时时间
- 减少图像尺寸和步数
- 检查服务器负载

### 调试模式

启用详细日志：
```bash
export LOG_LEVEL=DEBUG  # Linux/Mac
set LOG_LEVEL=DEBUG     # Windows
python start_comfyui_node.py
```

## 📊 性能优化

### 1. 调整并发数
```yaml
performance:
  max_concurrent_tasks: 1  # 降低并发减少资源占用
```

### 2. 优化生成参数
```python
# 快速生成设置
width = 512      # 较小尺寸
height = 512
steps = 15       # 较少步数
batch_size = 1   # 单张生成
```

### 3. 缓存优化
- 重用WebSocket连接
- 缓存常用模型信息
- 批量处理任务

## 🎉 成功标志

当您看到以下输出时，说明启动成功：

```
✅ 连接成功！
✅ 检查点模型 (X 个)
✅ 节点注册成功
🚀 ComfyUI算力节点启动完成
```

现在您的云端算力节点已经准备好处理AI图像生成任务了！

## 📞 获取帮助

如果遇到问题：
1. 查看日志文件：`logs/comfyui_node.log`
2. 运行诊断脚本：`python check_comfyui_service.py`
3. 检查配置文件：`comfyui_config.yaml`
4. 联系技术支持或提交Issue

---

🎨 **祝您使用愉快！开始您的AI绘画之旅吧！**
