# Dreamify AI绘画平台 - 快速启动指南

## 🚀 5分钟快速部署

### 前置条件
- Docker 20.10+
- Docker Compose 2.0+
- NVIDIA GPU + NVIDIA Docker (推荐)
- 至少8GB内存，16GB推荐

### 一键启动

```bash
# 1. 克隆项目
git clone https://github.com/your-repo/dreamify-ai.git
cd dreamify-ai

# 2. 配置环境
cp .env.example .env

# 3. 一键部署
chmod +x scripts/deploy.sh
./scripts/deploy.sh dev
```

### 访问服务

部署完成后，访问以下地址：

- **🎨 AI绘画平台**: http://localhost:3000
- **📊 API文档**: http://localhost:5000/docs
- **📈 监控面板**: http://localhost:3001 (admin/admin)
- **💾 文件管理**: http://localhost:9001 (minioadmin/minioadmin)

## 🖥️ 算力节点接入

### 1. 准备算力节点

在有GPU的机器上：

```bash
# 安装Python依赖
cd compute-node
pip install -r requirements.txt

# 配置节点信息
cp config.yaml.example config.yaml
nano config.yaml
```

### 2. 配置节点参数

编辑 `compute-node/config.yaml`：

```yaml
node_name: "我的RTX4090节点"
scheduler_url: "http://your-server:8001"  # 调度中心地址
gpu_model: "RTX 4090"
gpu_memory: 24576
max_concurrent_tasks: 4
supported_models:
  - "stable-diffusion-xl"
  - "flux-dev"
```

### 3. 启动算力节点

```bash
cd compute-node
python client.py
```

节点将自动连接到调度中心并开始接收任务。

## 🎯 核心功能

### AI图像生成
- **多模型支持**: Stable Diffusion XL, FLUX, 自定义模型
- **高度定制**: 尺寸、步数、批量、种子等参数
- **实时进度**: WebSocket实时显示生成进度
- **队列管理**: 智能任务调度和负载均衡

### 分布式算力
- **节点自动发现**: 算力节点自动注册和健康检查
- **负载均衡**: 基于GPU使用率的智能任务分发
- **故障恢复**: 节点故障自动转移任务
- **弹性扩展**: 支持动态添加/移除算力节点

### 用户体验
- **无需登录**: 游客模式快速体验
- **实时预览**: 生成过程实时显示
- **历史记录**: 个人作品管理和分享
- **响应式设计**: 支持桌面和移动设备

## 📊 系统架构

```
用户界面 (React/Next.js)
    ↓
API网关 (Express.js)
    ↓
任务队列 (Redis)
    ↓
算力调度器 (Node.js)
    ↓
算力节点集群 (Python + GPU)
    ↓
AI模型服务 (Stable Diffusion, FLUX)
```

## 🔧 高级配置

### 环境变量配置

关键环境变量说明：

```env
# 数据库配置
DATABASE_URL=postgresql://dreamify:password@localhost:5432/dreamify
REDIS_URL=redis://localhost:6379

# AI服务配置
AI_SERVICE_URL=http://localhost:8000
MODEL_CACHE_DIR=./models

# 算力节点配置
MAX_CONCURRENT_TASKS=4
TASK_TIMEOUT=300

# 文件存储配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
```

### 性能优化

1. **GPU优化**
   ```yaml
   # compute-node/config.yaml
   precision: "fp16"  # 使用半精度提升速度
   torch_compile: true  # 启用PyTorch编译优化
   memory_fraction: 0.9  # GPU显存使用比例
   ```

2. **并发优化**
   ```env
   # .env
   MAX_CONCURRENT_TASKS=4  # 根据GPU显存调整
   TASK_QUEUE_SIZE=100     # 任务队列大小
   WORKER_PROCESSES=2      # 工作进程数
   ```

3. **缓存优化**
   ```env
   # .env
   REDIS_MAX_MEMORY=2gb    # Redis内存限制
   MODEL_CACHE_SIZE=50gb   # 模型缓存大小
   IMAGE_CACHE_TTL=86400   # 图片缓存时间
   ```

## 🛠️ 常用命令

### 服务管理
```bash
# 启动开发环境
./scripts/deploy.sh dev

# 启动生产环境
./scripts/deploy.sh prod

# 停止服务
./scripts/deploy.sh stop

# 重启服务
./scripts/deploy.sh restart

# 查看日志
./scripts/deploy.sh logs

# 健康检查
./scripts/deploy.sh health
```

### 数据管理
```bash
# 备份数据
./scripts/deploy.sh backup

# 恢复数据
./scripts/deploy.sh restore backup_20231201_120000

# 清理资源
./scripts/deploy.sh cleanup
```

### 模型管理
```bash
# 查看已加载模型
curl http://localhost:8000/models

# 加载特定模型
curl -X POST http://localhost:8000/models/flux-dev/load

# 卸载模型
curl -X POST http://localhost:8000/models/stable-diffusion-xl/unload
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   sudo netstat -tulpn | grep :3000
   
   # 修改端口配置
   nano .env  # 修改相应端口
   ```

2. **GPU不可用**
   ```bash
   # 检查NVIDIA驱动
   nvidia-smi
   
   # 检查Docker GPU支持
   docker run --rm --gpus all nvidia/cuda:11.8-base nvidia-smi
   ```

3. **内存不足**
   ```bash
   # 检查系统内存
   free -h
   
   # 检查GPU内存
   nvidia-smi
   
   # 调整批处理大小
   nano compute-node/config.yaml  # 减少max_concurrent_tasks
   ```

4. **模型下载失败**
   ```bash
   # 手动下载模型
   cd models
   git lfs clone https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0
   
   # 设置代理（如需要）
   export HF_ENDPOINT=https://hf-mirror.com
   ```

### 性能监控

访问监控面板查看系统状态：
- **Grafana**: http://localhost:3001
- **Prometheus**: http://localhost:9090

关键指标：
- GPU使用率和温度
- 任务队列长度
- 响应时间
- 错误率

## 📚 更多资源

- [完整部署文档](docs/DEPLOYMENT.md)
- [API文档](docs/API.md)
- [开发指南](docs/DEVELOPMENT.md)
- [常见问题](docs/FAQ.md)

## 🤝 社区支持

- **GitHub**: https://github.com/your-repo/dreamify-ai
- **Discord**: https://discord.gg/dreamify
- **邮件**: <EMAIL>

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

🎉 **恭喜！您的AI绘画平台已经成功部署！**

现在您可以：
1. 访问 http://localhost:3000 开始创作
2. 添加更多算力节点扩展性能
3. 自定义模型和参数
4. 邀请朋友一起使用

有任何问题欢迎提交Issue或联系我们！
