@echo off
chcp 65001 >nul
echo 🚀 启动ComfyUI Web管理界面...
echo.

cd /d "%~dp0compute-node"

echo 📁 当前目录: %CD%
echo.

echo 🔍 检查Python...
python --version
if errorlevel 1 (
    echo ❌ Python未找到，尝试使用完整路径...
    D:\Python\python.exe --version
    if errorlevel 1 (
        echo ❌ 无法找到Python，请检查安装
        pause
        exit /b 1
    )
    echo ✅ 使用完整路径启动...
    D:\Python\python.exe web_ui.py
) else (
    echo ✅ 启动Web界面...
    python web_ui.py
)

pause
