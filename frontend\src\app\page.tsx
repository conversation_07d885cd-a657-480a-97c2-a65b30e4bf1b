'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>rkles, Zap, Users, HelpCircle, Globe, Image as ImageIcon } from 'lucide-react';
import { ImageGenerator } from '@/components/ImageGenerator';
import { ImageGallery } from '@/components/ImageGallery';
import { TaskProgress } from '@/components/TaskProgress';
import { Navigation } from '@/components/Navigation';
import { StatsSection } from '@/components/StatsSection';
import { useTaskStore } from '@/store/taskStore';
import { useAuthStore } from '@/store/authStore';

const demoImages = [
  '/images/demo-1.png',
  '/images/demo-2.png',
  '/images/demo-3.png',
  '/images/demo-6.png',
  '/images/demo-8.png',
  '/images/demo-10.png',
  '/images/demo-12.png'
];

export default function HomePage() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showGenerator, setShowGenerator] = useState(false);
  const { activeTasks } = useTaskStore();
  const { user } = useAuthStore();

  // 轮播图片
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % demoImages.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const handleStartCreating = () => {
    setShowGenerator(true);
    // 滚动到生成器部分
    document.getElementById('generator-section')?.scrollIntoView({ 
      behavior: 'smooth' 
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-800 via-slate-700 to-slate-800 overflow-x-hidden">
      {/* 导航栏 */}
      <Navigation />

      {/* 主要内容 */}
      <main className="transition-all duration-300 lg:ml-48 lg:pl-6 lg:pr-6 lg:w-[calc(100%-192px)]">
        
        {/* 英雄区域 */}
        <section className="relative min-h-screen flex items-center justify-center py-12 sm:py-20 overflow-hidden">
          {/* 背景效果 */}
          <div className="absolute inset-0 bg-gradient-to-br from-slate-800 via-slate-700 to-slate-800" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.2),rgba(255,255,255,0))]" />
          <div className="absolute inset-0 bg-[url('/images/grid.svg')] opacity-10" />

          <div className="w-full max-w-[1400px] mx-auto relative px-4 sm:px-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
              
              {/* 左侧内容 */}
              <motion.div 
                className="text-left"
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
              >
                {/* Logo和标题 */}
                <div className="flex items-center gap-4 mb-8 sm:mb-12">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-2xl blur-xl opacity-50 animate-pulse" />
                    <img 
                      alt="Dreamify Logo" 
                      width={64} 
                      height={64} 
                      className="rounded-2xl shadow-xl border border-cyan-400/30 relative z-10" 
                      src="/images/dreamify-logo.jpg"
                    />
                  </div>
                  <h2 className="text-4xl font-bold bg-gradient-to-r from-cyan-400 via-blue-400 to-indigo-400 bg-clip-text text-transparent">
                    Dreamify
                  </h2>
                </div>

                {/* 主标题 */}
                <h1 className="mb-6 sm:mb-8">
                  <span className="block text-3xl sm:text-4xl lg:text-6xl font-medium text-cyan-100 mb-3 sm:mb-4">
                    通过AI释放你的
                  </span>
                  <span className="block text-4xl sm:text-5xl lg:text-7xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                    无限想象力，只需一键！
                  </span>
                </h1>

                {/* 特性标签 */}
                <div className="flex flex-wrap gap-2 sm:gap-4 mb-6 sm:mb-8">
                  {[
                    { text: '快速生成', icon: Zap, color: 'from-cyan-400 to-blue-400' },
                    { text: '多种模型', icon: Sparkles, color: 'from-purple-400 to-pink-400' },
                    { text: '无需登录', icon: Users, color: 'from-green-400 to-emerald-400' },
                    { text: '高度定制', icon: ImageIcon, color: 'from-orange-400 to-red-400' },
                    { text: '支持中文', icon: Globe, color: 'from-yellow-400 to-amber-400' }
                  ].map((feature, index) => (
                    <motion.span
                      key={feature.text}
                      className={`px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r ${feature.color} text-white shadow-lg flex items-center gap-2`}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.2 + index * 0.1 }}
                    >
                      <feature.icon className="w-4 h-4" />
                      {feature.text}
                    </motion.span>
                  ))}
                </div>

                {/* 描述文本 */}
                <motion.p 
                  className="text-xl sm:text-2xl text-cyan-100 mb-6 sm:mb-8"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  由全国30台家用电脑的闲置4090显卡，
                  <span className="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent px-2">
                    免费无限制
                  </span>
                  提供分布式算力支持。
                </motion.p>

                {/* 操作按钮 */}
                <motion.div 
                  className="flex flex-col sm:flex-row gap-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                >
                  <button
                    onClick={handleStartCreating}
                    className="group px-10 py-4 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-2xl hover:from-cyan-400 hover:to-blue-400 transition-all duration-300 shadow-xl shadow-cyan-500/20 hover:shadow-2xl hover:shadow-cyan-500/30 hover:-translate-y-0.5 text-lg font-medium relative overflow-hidden"
                  >
                    <span className="relative z-10 flex items-center gap-2">
                      <Sparkles className="w-5 h-5" />
                      开始创作
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </button>
                  
                  <button className="group px-10 py-4 border-2 border-cyan-400/50 text-cyan-300 rounded-2xl hover:bg-cyan-400/10 transition-all duration-300 text-lg font-medium relative overflow-hidden">
                    <span className="relative z-10 flex items-center gap-2">
                      <HelpCircle className="w-5 h-5" />
                      常见问题
                    </span>
                    <div className="absolute inset-0 bg-cyan-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </button>
                </motion.div>
              </motion.div>

              {/* 右侧图片展示 */}
              <motion.div 
                className="relative"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <div className="aspect-square rounded-2xl sm:rounded-3xl overflow-hidden shadow-xl sm:shadow-2xl bg-slate-700/50 border border-cyan-400/30 transform hover:scale-[1.02] transition-transform duration-500">
                  <AnimatePresence mode="wait">
                    {demoImages.map((image, index) => (
                      index === currentImageIndex && (
                        <motion.img
                          key={image}
                          src={image}
                          alt={`AI生成的图像示例 ${index + 1}`}
                          className="w-full h-full object-cover"
                          initial={{ opacity: 0, scale: 1.1 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.9 }}
                          transition={{ duration: 1 }}
                        />
                      )
                    ))}
                  </AnimatePresence>
                </div>

                {/* 图片指示器 */}
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                  <div className="flex gap-4 bg-slate-700/80 backdrop-blur-xl px-8 py-3 rounded-2xl shadow-xl border border-cyan-400/30">
                    {demoImages.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-3 h-3 rounded-full transition-all duration-300 transform hover:scale-125 ${
                          index === currentImageIndex 
                            ? 'bg-cyan-400 scale-125' 
                            : 'bg-cyan-400/20 hover:bg-cyan-400/40'
                        }`}
                        aria-label={`切换到图片 ${index + 1}`}
                      />
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* 统计数据区域 */}
        <StatsSection />

        {/* 图像生成器区域 */}
        <section id="generator-section" className="py-12 sm:py-20 relative">
          <div className="absolute inset-0 bg-[url('/images/grid.svg')] opacity-5" />
          <div className="w-full max-w-[1400px] mx-auto relative px-4 sm:px-6">
            <ImageGenerator />
          </div>
        </section>

        {/* 任务进度显示 */}
        <AnimatePresence>
          {activeTasks.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 50 }}
              className="fixed bottom-4 right-4 z-50"
            >
              <TaskProgress />
            </motion.div>
          )}
        </AnimatePresence>

        {/* 图片画廊 */}
        {user && (
          <section className="py-12 sm:py-20 relative">
            <div className="w-full max-w-[1400px] mx-auto relative px-4 sm:px-6">
              <ImageGallery />
            </div>
          </section>
        )}
      </main>
    </div>
  );
}
