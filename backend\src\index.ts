import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import dotenv from 'dotenv';

import { errorHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { logger } from './utils/logger';
import { connectRedis } from './config/redis';
import { connectDatabase } from './config/database';
import { initializeMinIO } from './config/minio';
import { setupMetrics } from './utils/metrics';

// 路由导入
import authRoutes from './routes/auth';
import taskRoutes from './routes/tasks';
import imageRoutes from './routes/images';
import nodeRoutes from './routes/nodes';
import userRoutes from './routes/users';
import healthRoutes from './routes/health';

// 服务导入
import { TaskService } from './services/TaskService';
import { NodeService } from './services/NodeService';
import { WebSocketService } from './services/WebSocketService';

dotenv.config();

class App {
  public app: express.Application;
  public server: any;
  public io: SocketIOServer;
  private taskService: TaskService;
  private nodeService: NodeService;
  private wsService: WebSocketService;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:3000",
        methods: ["GET", "POST"]
      }
    });

    this.initializeServices();
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private async initializeServices(): Promise<void> {
    try {
      // 初始化数据库连接
      await connectDatabase();
      logger.info('Database connected successfully');

      // 初始化Redis连接
      await connectRedis();
      logger.info('Redis connected successfully');

      // 初始化MinIO
      await initializeMinIO();
      logger.info('MinIO initialized successfully');

      // 初始化服务
      this.taskService = new TaskService();
      this.nodeService = new NodeService();
      this.wsService = new WebSocketService(this.io);

      // 设置监控指标
      setupMetrics();
      logger.info('Metrics setup completed');

    } catch (error) {
      logger.error('Failed to initialize services:', error);
      process.exit(1);
    }
  }

  private initializeMiddlewares(): void {
    // 安全中间件
    this.app.use(helmet());
    
    // CORS配置
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || "http://localhost:3000",
      credentials: true
    }));

    // 压缩响应
    this.app.use(compression());

    // 请求日志
    this.app.use(morgan('combined', {
      stream: { write: (message) => logger.info(message.trim()) }
    }));

    // 请求体解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 速率限制
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 限制每个IP 100个请求
      message: {
        error: 'Too many requests from this IP, please try again later.'
      }
    });
    this.app.use('/api/', limiter);

    // 健康检查路由（不需要认证）
    this.app.use('/health', healthRoutes);

    // API路由前缀
    this.app.use('/api/v1', this.getApiRoutes());
  }

  private getApiRoutes(): express.Router {
    const router = express.Router();

    // 公开路由
    router.use('/auth', authRoutes);
    
    // 需要认证的路由
    router.use('/tasks', authMiddleware, taskRoutes);
    router.use('/images', authMiddleware, imageRoutes);
    router.use('/users', authMiddleware, userRoutes);
    
    // 管理员路由
    router.use('/nodes', authMiddleware, nodeRoutes);

    return router;
  }

  private initializeRoutes(): void {
    // 根路径
    this.app.get('/', (req, res) => {
      res.json({
        message: 'Dreamify AI绘画平台 API',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString()
      });
    });

    // 404处理
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl
      });
    });
  }

  private initializeErrorHandling(): void {
    this.app.use(errorHandler);
  }

  public listen(): void {
    const port = process.env.API_PORT || 5000;
    const host = process.env.API_HOST || '0.0.0.0';

    this.server.listen(port, host, () => {
      logger.info(`🚀 Server running on http://${host}:${port}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    });

    // 优雅关闭
    process.on('SIGTERM', this.gracefulShutdown.bind(this));
    process.on('SIGINT', this.gracefulShutdown.bind(this));
  }

  private async gracefulShutdown(signal: string): Promise<void> {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);

    this.server.close(() => {
      logger.info('HTTP server closed');
      process.exit(0);
    });

    // 强制关闭超时
    setTimeout(() => {
      logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  }
}

// 启动应用
const app = new App();
app.listen();

export default app;
