import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import Bull, { Queue, Job } from 'bull';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { ApiError } from '../utils/ApiError';
import { NodeService } from './NodeService';
import { WebSocketService } from './WebSocketService';

interface TaskData {
  id: string;
  userId: string;
  prompt: string;
  model: string;
  width: number;
  height: number;
  steps: number;
  batch_size: number;
  cfg_scale?: number;
  seed?: number;
  negative_prompt?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  priority?: number;
  estimatedTime?: number;
  queuePosition?: number;
  nodeId?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TaskProgress {
  taskId: string;
  status: string;
  progress: number;
  estimatedTimeRemaining?: number;
  currentStep?: number;
  totalSteps?: number;
  nodeId?: string;
  error?: string;
}

interface UserQuota {
  userId: string;
  daily: number;
  remaining: number;
  resetTime: Date;
}

export class TaskService {
  private prisma: PrismaClient;
  private redis: Redis;
  private taskQueue: Queue;
  private nodeService: NodeService;
  private wsService: WebSocketService;

  constructor() {
    this.prisma = new PrismaClient();
    this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
    
    // 初始化任务队列
    this.taskQueue = new Bull('image-generation', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      }
    });

    this.nodeService = new NodeService();
    this.setupQueueProcessors();
  }

  private setupQueueProcessors(): void {
    // 处理任务队列
    this.taskQueue.process('generate-image', async (job: Job) => {
      return await this.processImageGeneration(job);
    });

    // 队列事件监听
    this.taskQueue.on('completed', (job: Job, result: any) => {
      logger.info(`Task completed: ${job.data.taskId}`);
      this.handleTaskCompleted(job.data.taskId, result);
    });

    this.taskQueue.on('failed', (job: Job, err: Error) => {
      logger.error(`Task failed: ${job.data.taskId}`, err);
      this.handleTaskFailed(job.data.taskId, err.message);
    });

    this.taskQueue.on('progress', (job: Job, progress: number) => {
      this.handleTaskProgress(job.data.taskId, progress);
    });
  }

  async createTask(taskData: Omit<TaskData, 'id'>): Promise<TaskData> {
    const taskId = uuidv4();
    
    try {
      // 估算处理时间
      const estimatedTime = this.estimateProcessingTime(taskData);
      
      // 创建任务记录
      const task = await this.prisma.task.create({
        data: {
          id: taskId,
          userId: taskData.userId,
          prompt: taskData.prompt,
          model: taskData.model,
          width: taskData.width,
          height: taskData.height,
          steps: taskData.steps,
          batchSize: taskData.batch_size,
          cfgScale: taskData.cfg_scale || 7.5,
          seed: taskData.seed || -1,
          negativePrompt: taskData.negative_prompt || '',
          status: 'pending',
          estimatedTime,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      // 更新用户配额
      await this.updateUserQuota(taskData.userId, taskData.batch_size);

      return {
        ...taskData,
        id: taskId,
        status: 'pending' as const,
        estimatedTime,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt
      };

    } catch (error) {
      logger.error('Failed to create task:', error);
      throw new ApiError(500, 'Failed to create task');
    }
  }

  async addToQueue(task: TaskData): Promise<void> {
    try {
      // 计算优先级（VIP用户、付费用户等可以有更高优先级）
      const priority = await this.calculateTaskPriority(task.userId);
      
      // 添加到队列
      const job = await this.taskQueue.add('generate-image', {
        taskId: task.id,
        ...task
      }, {
        priority,
        delay: 0
      });

      // 获取队列位置
      const queuePosition = await this.getQueuePosition(job.id);
      
      // 更新任务状态
      await this.updateTaskStatus(task.id, 'pending', 0, {
        queuePosition,
        jobId: job.id
      });

      logger.info(`Task ${task.id} added to queue with priority ${priority}, position ${queuePosition}`);

    } catch (error) {
      logger.error('Failed to add task to queue:', error);
      throw new ApiError(500, 'Failed to queue task');
    }
  }

  private async processImageGeneration(job: Job): Promise<any> {
    const { taskId, ...taskData } = job.data;
    
    try {
      // 更新任务状态为处理中
      await this.updateTaskStatus(taskId, 'processing', 0);

      // 选择最佳算力节点
      const node = await this.nodeService.selectBestNode(taskData.model);
      if (!node) {
        throw new Error('No available compute nodes');
      }

      // 分配任务到节点
      const result = await this.nodeService.assignTask(node.id, {
        taskId,
        ...taskData
      });

      // 监控任务进度
      await this.monitorTaskProgress(taskId, node.id);

      return result;

    } catch (error) {
      logger.error(`Task processing failed: ${taskId}`, error);
      throw error;
    }
  }

  private async monitorTaskProgress(taskId: string, nodeId: string): Promise<void> {
    const progressKey = `task:progress:${taskId}`;
    
    // 设置进度监控
    const interval = setInterval(async () => {
      try {
        const progress = await this.redis.get(progressKey);
        if (progress) {
          const progressData = JSON.parse(progress);
          await this.handleTaskProgress(taskId, progressData.progress);
          
          // 如果任务完成或失败，停止监控
          if (progressData.status === 'completed' || progressData.status === 'failed') {
            clearInterval(interval);
          }
        }
      } catch (error) {
        logger.error(`Progress monitoring error for task ${taskId}:`, error);
      }
    }, 2000); // 每2秒检查一次

    // 设置超时
    setTimeout(() => {
      clearInterval(interval);
    }, 600000); // 10分钟超时
  }

  async getUserTasks(
    filters: { userId: string; status?: string; model?: string },
    page: number = 1,
    limit: number = 20
  ): Promise<{ tasks: any[]; total: number }> {
    try {
      const where: any = { userId: filters.userId };
      
      if (filters.status) {
        where.status = filters.status;
      }
      
      if (filters.model) {
        where.model = filters.model;
      }

      const [tasks, total] = await Promise.all([
        this.prisma.task.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
          include: {
            images: true
          }
        }),
        this.prisma.task.count({ where })
      ]);

      return { tasks, total };

    } catch (error) {
      logger.error('Failed to get user tasks:', error);
      throw new ApiError(500, 'Failed to retrieve tasks');
    }
  }

  async getTaskById(taskId: string, userId: string): Promise<any> {
    try {
      const task = await this.prisma.task.findFirst({
        where: {
          id: taskId,
          userId
        },
        include: {
          images: true
        }
      });

      if (!task) {
        return null;
      }

      // 获取实时进度
      const progress = await this.getTaskProgress(taskId, userId);
      
      return {
        ...task,
        progress: progress?.progress || 0,
        currentStatus: progress?.status || task.status
      };

    } catch (error) {
      logger.error('Failed to get task by ID:', error);
      throw new ApiError(500, 'Failed to retrieve task');
    }
  }

  async getTaskProgress(taskId: string, userId: string): Promise<TaskProgress | null> {
    try {
      // 验证任务所有权
      const task = await this.prisma.task.findFirst({
        where: { id: taskId, userId }
      });

      if (!task) {
        return null;
      }

      // 从Redis获取实时进度
      const progressKey = `task:progress:${taskId}`;
      const progressData = await this.redis.get(progressKey);

      if (progressData) {
        return JSON.parse(progressData);
      }

      // 如果Redis中没有进度数据，返回数据库中的状态
      return {
        taskId,
        status: task.status,
        progress: task.status === 'completed' ? 100 : 0
      };

    } catch (error) {
      logger.error('Failed to get task progress:', error);
      return null;
    }
  }

  async cancelTask(taskId: string): Promise<void> {
    try {
      // 更新数据库状态
      await this.prisma.task.update({
        where: { id: taskId },
        data: {
          status: 'cancelled',
          updatedAt: new Date()
        }
      });

      // 从队列中移除任务
      const jobs = await this.taskQueue.getJobs(['waiting', 'delayed']);
      const job = jobs.find(j => j.data.taskId === taskId);
      
      if (job) {
        await job.remove();
        logger.info(`Task ${taskId} removed from queue`);
      }

      // 通知WebSocket客户端
      if (this.wsService) {
        this.wsService.notifyTaskUpdate(taskId, {
          status: 'cancelled',
          progress: 0
        });
      }

    } catch (error) {
      logger.error('Failed to cancel task:', error);
      throw new ApiError(500, 'Failed to cancel task');
    }
  }

  async retryTask(taskId: string): Promise<void> {
    try {
      const task = await this.prisma.task.findUnique({
        where: { id: taskId }
      });

      if (!task) {
        throw new ApiError(404, 'Task not found');
      }

      if (task.status !== 'failed') {
        throw new ApiError(400, 'Can only retry failed tasks');
      }

      // 重置任务状态
      await this.prisma.task.update({
        where: { id: taskId },
        data: {
          status: 'pending',
          updatedAt: new Date()
        }
      });

      // 重新添加到队列
      await this.addToQueue(task as any);

      logger.info(`Task ${taskId} queued for retry`);

    } catch (error) {
      logger.error('Failed to retry task:', error);
      throw error;
    }
  }

  async getUserQuota(userId: string): Promise<UserQuota> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // 获取今日任务数量
      const todayTasks = await this.prisma.task.count({
        where: {
          userId,
          createdAt: {
            gte: today,
            lt: tomorrow
          }
        }
      });

      // 获取用户配额设置（这里简化为固定值，实际应该从用户表获取）
      const dailyLimit = 50; // 每日50张图片
      const remaining = Math.max(0, dailyLimit - todayTasks);

      return {
        userId,
        daily: dailyLimit,
        remaining,
        resetTime: tomorrow
      };

    } catch (error) {
      logger.error('Failed to get user quota:', error);
      throw new ApiError(500, 'Failed to get quota information');
    }
  }

  private async updateUserQuota(userId: string, imageCount: number): Promise<void> {
    // 这里可以实现配额扣减逻辑
    // 例如：更新用户表中的配额字段
    logger.info(`User ${userId} quota updated: -${imageCount} images`);
  }

  private estimateProcessingTime(taskData: any): number {
    // 基于模型、尺寸、步数等估算处理时间
    const baseTime = 30; // 基础30秒
    const stepMultiplier = taskData.steps * 0.5; // 每步0.5秒
    const sizeMultiplier = (taskData.width * taskData.height) / (512 * 512); // 相对于512x512的倍数
    const batchMultiplier = taskData.batch_size * 0.8; // 批量处理有一定优化
    
    return Math.round(baseTime + stepMultiplier * sizeMultiplier * batchMultiplier);
  }

  private async calculateTaskPriority(userId: string): number {
    // 基于用户等级、付费状态等计算优先级
    // 数值越大优先级越高
    return 1; // 默认优先级
  }

  private async getQueuePosition(jobId: string): Promise<number> {
    const waitingJobs = await this.taskQueue.getJobs(['waiting']);
    const position = waitingJobs.findIndex(job => job.id === jobId);
    return position >= 0 ? position + 1 : 0;
  }

  private async updateTaskStatus(
    taskId: string, 
    status: string, 
    progress: number, 
    metadata?: any
  ): Promise<void> {
    try {
      // 更新数据库
      await this.prisma.task.update({
        where: { id: taskId },
        data: {
          status,
          updatedAt: new Date(),
          ...(metadata && { metadata })
        }
      });

      // 更新Redis进度缓存
      const progressKey = `task:progress:${taskId}`;
      await this.redis.setex(progressKey, 3600, JSON.stringify({
        taskId,
        status,
        progress,
        timestamp: Date.now(),
        ...metadata
      }));

      // 通知WebSocket客户端
      if (this.wsService) {
        this.wsService.notifyTaskUpdate(taskId, {
          status,
          progress,
          ...metadata
        });
      }

    } catch (error) {
      logger.error('Failed to update task status:', error);
    }
  }

  private async handleTaskCompleted(taskId: string, result: any): Promise<void> {
    await this.updateTaskStatus(taskId, 'completed', 100, result);
  }

  private async handleTaskFailed(taskId: string, error: string): Promise<void> {
    await this.updateTaskStatus(taskId, 'failed', 0, { error });
  }

  private async handleTaskProgress(taskId: string, progress: number): Promise<void> {
    await this.updateTaskStatus(taskId, 'processing', progress);
  }
}
