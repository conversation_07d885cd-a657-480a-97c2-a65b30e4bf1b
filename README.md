# Dreamify AI绘画平台 - 云端算力版

## 项目概述

Dreamify是一个基于分布式云端算力的AI绘画平台，支持多个RTX 4090节点提供高性能图像生成服务。

## 系统架构

### 核心组件
- **前端**: React/Next.js 响应式Web界面
- **API网关**: Express.js/FastAPI 统一接口管理
- **任务调度**: Redis队列 + 智能负载均衡
- **算力节点**: 分布式RTX 4090 GPU集群
- **模型服务**: Stable Diffusion, FLUX等多模型支持
- **存储**: PostgreSQL + MinIO对象存储

### 技术栈
- **前端**: React 18, Next.js 14, TypeScript, Tailwind CSS
- **后端**: Node.js, Express.js, TypeScript
- **数据库**: PostgreSQL, Redis
- **消息队列**: Redis/RabbitMQ
- **AI框架**: <PERSON>y<PERSON><PERSON>ch, Diffusers, ComfyUI
- **容器化**: Docker, Docker Compose
- **监控**: Prometheus, Grafana

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- Docker & Docker Compose
- NVIDIA GPU (RTX 4090推荐)
- CUDA 11.8+

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/dreamify-ai.git
cd dreamify-ai
```

2. **安装依赖**
```bash
# 前端依赖
cd frontend
npm install

# 后端依赖
cd ../backend
npm install

# AI服务依赖
cd ../ai-service
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，配置数据库、Redis等连接信息
```

4. **启动服务**
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 或分别启动各服务
npm run dev:frontend
npm run dev:backend
python ai-service/main.py
```

## 目录结构

```
dreamify-ai/
├── frontend/                 # React前端应用
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/                  # Node.js后端API
│   ├── src/
│   ├── config/
│   └── package.json
├── ai-service/              # Python AI模型服务
│   ├── models/
│   ├── services/
│   └── requirements.txt
├── compute-node/            # 算力节点客户端
│   ├── client.py
│   └── config.yaml
├── docker/                  # Docker配置文件
├── docs/                    # 项目文档
└── scripts/                 # 部署脚本
```

## 算力节点接入

### 节点注册
1. 在算力节点上安装客户端
2. 配置节点信息（GPU型号、内存等）
3. 启动节点客户端，自动注册到调度中心

### 负载均衡
- 基于GPU使用率的智能调度
- 任务优先级管理
- 故障自动转移

## API文档

### 图像生成接口
```
POST /api/v1/generate
Content-Type: application/json

{
  "prompt": "a beautiful landscape",
  "model": "stable-diffusion-xl",
  "width": 1024,
  "height": 1024,
  "steps": 30,
  "batch_size": 4
}
```

### 任务状态查询
```
GET /api/v1/tasks/{task_id}
```

## 部署指南

### 生产环境部署
1. 配置负载均衡器（Nginx）
2. 设置SSL证书
3. 配置监控和日志
4. 数据库备份策略

### 算力节点扩展
- 支持动态添加/移除节点
- 自动发现和注册
- 健康检查和故障恢复

## 监控和运维

- **性能监控**: GPU使用率、内存占用、任务处理时间
- **业务监控**: 用户请求量、成功率、错误统计
- **告警机制**: 节点故障、队列积压、异常错误

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

- 项目主页: https://dreamify.example.com
- 技术支持: <EMAIL>
- 开发者社区: https://discord.gg/dreamify
