#!/usr/bin/env python3
"""
Dreamify AI模型服务
提供统一的AI图像生成接口，支持多种模型
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import torch
from contextlib import asynccontextmanager

# 导入模型管理器
from models.model_manager import ModelManager
from models.stable_diffusion import StableDiffusionModel
from models.flux import FluxModel
from services.task_processor import TaskProcessor
from services.redis_client import RedisClient
from utils.config import Config
from utils.logger import setup_logger

# 配置日志
logger = setup_logger(__name__)

# 全局变量
model_manager: Optional[ModelManager] = None
task_processor: Optional[TaskProcessor] = None
redis_client: Optional[RedisClient] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global model_manager, task_processor, redis_client
    
    try:
        # 启动时初始化
        logger.info("初始化AI模型服务...")
        
        # 初始化Redis客户端
        redis_client = RedisClient()
        await redis_client.connect()
        
        # 初始化模型管理器
        model_manager = ModelManager()
        await model_manager.initialize()
        
        # 初始化任务处理器
        task_processor = TaskProcessor(model_manager, redis_client)
        await task_processor.start()
        
        logger.info("AI模型服务启动完成")
        yield
        
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)
    finally:
        # 关闭时清理
        logger.info("正在关闭AI模型服务...")
        
        if task_processor:
            await task_processor.stop()
        
        if model_manager:
            await model_manager.cleanup()
        
        if redis_client:
            await redis_client.disconnect()
        
        logger.info("AI模型服务已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="Dreamify AI模型服务",
    description="提供AI图像生成服务",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class GenerateImageRequest(BaseModel):
    prompt: str = Field(..., description="图像描述提示词")
    model: str = Field("stable-diffusion-xl", description="使用的AI模型")
    width: int = Field(1024, ge=64, le=1920, description="图像宽度")
    height: int = Field(1024, ge=64, le=1920, description="图像高度")
    steps: int = Field(30, ge=1, le=100, description="推理步数")
    batch_size: int = Field(1, ge=1, le=4, description="批量大小")
    cfg_scale: float = Field(7.5, ge=1.0, le=20.0, description="CFG引导强度")
    seed: int = Field(-1, description="随机种子，-1为随机")
    negative_prompt: str = Field("", description="负面提示词")
    scheduler: str = Field("DPMSolverMultistepScheduler", description="采样器")

class ModelInfo(BaseModel):
    id: str
    name: str
    description: str
    type: str
    loaded: bool
    memory_usage: Optional[float] = None
    supported_sizes: List[tuple]
    max_batch_size: int

class GenerationResult(BaseModel):
    task_id: str
    status: str
    images: List[Dict[str, Any]] = []
    metadata: Dict[str, Any] = {}
    error: Optional[str] = None

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "Dreamify AI模型服务",
        "version": "1.0.0",
        "status": "running",
        "models_loaded": len(model_manager.loaded_models) if model_manager else 0
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型管理器未初始化")
    
    # 检查GPU状态
    gpu_available = torch.cuda.is_available()
    gpu_count = torch.cuda.device_count() if gpu_available else 0
    
    # 检查内存使用
    memory_info = {}
    if gpu_available:
        for i in range(gpu_count):
            memory_info[f"gpu_{i}"] = {
                "allocated": torch.cuda.memory_allocated(i) / 1024**3,  # GB
                "cached": torch.cuda.memory_reserved(i) / 1024**3,      # GB
                "total": torch.cuda.get_device_properties(i).total_memory / 1024**3  # GB
            }
    
    return {
        "status": "healthy",
        "gpu_available": gpu_available,
        "gpu_count": gpu_count,
        "memory_info": memory_info,
        "loaded_models": list(model_manager.loaded_models.keys()),
        "task_processor_running": task_processor.is_running if task_processor else False
    }

@app.get("/models", response_model=List[ModelInfo])
async def list_models():
    """获取可用模型列表"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型管理器未初始化")
    
    models = []
    for model_id, model_config in model_manager.available_models.items():
        model_info = ModelInfo(
            id=model_id,
            name=model_config["name"],
            description=model_config["description"],
            type=model_config["type"],
            loaded=model_id in model_manager.loaded_models,
            supported_sizes=model_config.get("supported_sizes", [(1024, 1024)]),
            max_batch_size=model_config.get("max_batch_size", 4)
        )
        
        # 如果模型已加载，获取内存使用情况
        if model_info.loaded:
            model_instance = model_manager.loaded_models[model_id]
            if hasattr(model_instance, 'get_memory_usage'):
                model_info.memory_usage = model_instance.get_memory_usage()
        
        models.append(model_info)
    
    return models

@app.post("/models/{model_id}/load")
async def load_model(model_id: str):
    """加载指定模型"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型管理器未初始化")
    
    try:
        await model_manager.load_model(model_id)
        return {"message": f"模型 {model_id} 加载成功"}
    except Exception as e:
        logger.error(f"加载模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"加载模型失败: {str(e)}")

@app.post("/models/{model_id}/unload")
async def unload_model(model_id: str):
    """卸载指定模型"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型管理器未初始化")
    
    try:
        await model_manager.unload_model(model_id)
        return {"message": f"模型 {model_id} 卸载成功"}
    except Exception as e:
        logger.error(f"卸载模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"卸载模型失败: {str(e)}")

@app.post("/generate", response_model=GenerationResult)
async def generate_image(
    request: GenerateImageRequest,
    background_tasks: BackgroundTasks
):
    """生成图像（异步）"""
    if not model_manager or not task_processor:
        raise HTTPException(status_code=503, detail="服务未就绪")
    
    try:
        # 验证模型是否可用
        if request.model not in model_manager.available_models:
            raise HTTPException(status_code=400, detail=f"不支持的模型: {request.model}")
        
        # 创建任务
        task_id = await task_processor.create_task(request.dict())
        
        return GenerationResult(
            task_id=task_id,
            status="queued",
            metadata={"estimated_time": request.steps * 2}  # 估算时间
        )
        
    except Exception as e:
        logger.error(f"创建生成任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@app.post("/generate/sync", response_model=GenerationResult)
async def generate_image_sync(request: GenerateImageRequest):
    """生成图像（同步）"""
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型管理器未初始化")
    
    try:
        # 确保模型已加载
        if request.model not in model_manager.loaded_models:
            await model_manager.load_model(request.model)
        
        # 获取模型实例
        model = model_manager.loaded_models[request.model]
        
        # 生成图像
        result = await model.generate(
            prompt=request.prompt,
            width=request.width,
            height=request.height,
            steps=request.steps,
            batch_size=request.batch_size,
            cfg_scale=request.cfg_scale,
            seed=request.seed,
            negative_prompt=request.negative_prompt,
            scheduler=request.scheduler
        )
        
        return GenerationResult(
            task_id="sync",
            status="completed",
            images=result["images"],
            metadata=result["metadata"]
        )
        
    except Exception as e:
        logger.error(f"同步生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成失败: {str(e)}")

@app.get("/tasks/{task_id}", response_model=GenerationResult)
async def get_task_status(task_id: str):
    """获取任务状态"""
    if not task_processor:
        raise HTTPException(status_code=503, detail="任务处理器未初始化")
    
    try:
        task_info = await task_processor.get_task_status(task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return GenerationResult(**task_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@app.delete("/tasks/{task_id}")
async def cancel_task(task_id: str):
    """取消任务"""
    if not task_processor:
        raise HTTPException(status_code=503, detail="任务处理器未初始化")
    
    try:
        success = await task_processor.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        
        return {"message": f"任务 {task_id} 已取消"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

@app.get("/metrics")
async def get_metrics():
    """获取服务指标"""
    if not model_manager or not task_processor:
        raise HTTPException(status_code=503, detail="服务未就绪")
    
    # GPU指标
    gpu_metrics = {}
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            gpu_metrics[f"gpu_{i}"] = {
                "utilization": torch.cuda.utilization(i),
                "memory_allocated": torch.cuda.memory_allocated(i),
                "memory_reserved": torch.cuda.memory_reserved(i),
                "temperature": 0  # 需要nvidia-ml-py获取
            }
    
    # 任务指标
    task_metrics = await task_processor.get_metrics()
    
    # 模型指标
    model_metrics = {
        "loaded_models": len(model_manager.loaded_models),
        "available_models": len(model_manager.available_models),
        "model_memory_usage": sum(
            model.get_memory_usage() 
            for model in model_manager.loaded_models.values()
            if hasattr(model, 'get_memory_usage')
        )
    }
    
    return {
        "gpu": gpu_metrics,
        "tasks": task_metrics,
        "models": model_metrics,
        "timestamp": asyncio.get_event_loop().time()
    }

# 错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return {
        "error": "内部服务器错误",
        "detail": str(exc) if Config.DEBUG else "请联系管理员"
    }

def main():
    """主函数"""
    # 配置日志级别
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    
    # 启动服务
    uvicorn.run(
        "main:app",
        host=Config.HOST,
        port=Config.PORT,
        log_level=log_level.lower(),
        reload=Config.DEBUG,
        workers=1  # AI服务通常使用单进程
    )

if __name__ == "__main__":
    main()
