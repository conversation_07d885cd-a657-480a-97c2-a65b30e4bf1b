{"name": "dreamify-backend", "version": "1.0.0", "description": "Dreamify AI绘画平台后端API服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:push": "npx prisma db push", "db:studio": "npx prisma studio"}, "keywords": ["ai", "image-generation", "stable-diffusion", "api", "nodejs"], "author": "Dreamify Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "uuid": "^9.0.1", "ws": "^8.14.2", "socket.io": "^4.7.4", "redis": "^4.6.10", "ioredis": "^5.3.2", "bull": "^4.12.2", "prisma": "^5.6.0", "@prisma/client": "^5.6.0", "minio": "^7.1.3", "axios": "^1.6.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "express-winston": "^4.2.0", "winston": "^3.11.0", "prom-client": "^15.0.0", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@types/compression": "^1.7.5", "@types/node": "^20.9.0", "@types/node-cron": "^3.0.11", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}