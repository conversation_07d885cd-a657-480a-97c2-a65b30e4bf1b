#!/usr/bin/env python3
"""
测试ComfyUI云端算力连接
"""

import asyncio
import sys
from comfyui_client import ComfyUIClient

async def test_connection():
    """测试连接"""
    print("🔗 测试ComfyUI连接...")

    client = ComfyUIClient("https://koomfonjr8-8188.cnb.run")

    try:
        # 连接服务
        await client.connect()
        print("✅ 连接成功！")

        # 详细诊断
        print("\n🔍 详细诊断...")

        # 1. 测试基本API
        try:
            async with client.session.get(f"{client.server_url}/object_info") as response:
                print(f"📡 /object_info 状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"📊 API响应数据大小: {len(str(data))} 字符")

                    # 查看所有可用的节点类型
                    print(f"🔧 可用节点类型: {len(data)} 个")

                    # 查找检查点加载器
                    checkpoint_loaders = [key for key in data.keys() if 'checkpoint' in key.lower() or 'loader' in key.lower()]
                    print(f"📦 检查点加载器: {checkpoint_loaders}")

                    # 查看CheckpointLoaderSimple的详细信息
                    if "CheckpointLoaderSimple" in data:
                        loader_info = data["CheckpointLoaderSimple"]
                        print(f"✅ 找到CheckpointLoaderSimple")
                        print(f"   输入参数: {list(loader_info.get('input', {}).keys())}")

                        if 'input' in loader_info and 'ckpt_name' in loader_info['input']:
                            ckpt_info = loader_info['input']['ckpt_name']
                            print(f"   检查点参数类型: {type(ckpt_info)}")
                            print(f"   检查点参数内容: {ckpt_info}")
                    else:
                        print("❌ 未找到CheckpointLoaderSimple")

                else:
                    error_text = await response.text()
                    print(f"❌ API调用失败: {error_text}")
        except Exception as e:
            print(f"❌ API诊断失败: {e}")

        # 2. 尝试获取队列信息
        try:
            async with client.session.get(f"{client.server_url}/queue") as response:
                print(f"📋 /queue 状态码: {response.status}")
                if response.status == 200:
                    queue_data = await response.json()
                    print(f"📊 队列信息: {queue_data}")
        except Exception as e:
            print(f"⚠️ 队列信息获取失败: {e}")

        # 3. 尝试获取历史记录
        try:
            async with client.session.get(f"{client.server_url}/history") as response:
                print(f"📜 /history 状态码: {response.status}")
        except Exception as e:
            print(f"⚠️ 历史记录获取失败: {e}")

        # 获取可用模型
        print("\n📋 获取可用模型...")
        models = await client.get_models()

        print(f"✅ 检查点模型 ({len(models['checkpoints'])} 个):")
        if models['checkpoints']:
            for i, model in enumerate(models['checkpoints'][:5]):  # 只显示前5个
                print(f"   {i+1}. {model}")
            if len(models['checkpoints']) > 5:
                print(f"   ... 还有 {len(models['checkpoints']) - 5} 个模型")
        else:
            print("   ⚠️ 没有检测到检查点模型")

        print(f"\n✅ VAE模型 ({len(models['vaes'])} 个):")
        if models['vaes']:
            for i, vae in enumerate(models['vaes'][:3]):  # 只显示前3个
                print(f"   {i+1}. {vae}")
            if len(models['vaes']) > 3:
                print(f"   ... 还有 {len(models['vaes']) - 3} 个VAE")
        else:
            print("   ⚠️ 没有检测到VAE模型")

        return True

    except Exception as e:
        print(f"❌ 连接失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await client.disconnect()

async def test_generation():
    """测试图像生成"""
    print("\n🎨 测试图像生成...")
    
    client = ComfyUIClient("https://koomfonjr8-8188.cnb.run")
    
    try:
        # 连接服务
        await client.connect()
        
        # 生成测试图像
        print("⏳ 开始生成图像...")
        result = await client.generate_image(
            prompt="a cute cat sitting on a table, highly detailed, photorealistic",
            negative_prompt="blurry, low quality, distorted",
            width=512,  # 使用较小尺寸加快测试
            height=512,
            steps=15,   # 使用较少步数加快测试
            cfg_scale=7.5
        )
        
        print(f"✅ 生成完成！共 {len(result['images'])} 张图片")
        
        # 保存图片到本地
        import base64
        for i, image in enumerate(result['images']):
            image_data = base64.b64decode(image['data'])
            filename = f"test_image_{i}.png"
            with open(filename, "wb") as f:
                f.write(image_data)
            print(f"💾 图片已保存: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return False
    finally:
        await client.disconnect()

async def main():
    """主测试函数"""
    print("🚀 ComfyUI云端算力测试")
    print("=" * 50)
    
    # 测试连接
    connection_ok = await test_connection()
    
    if not connection_ok:
        print("\n❌ 连接测试失败，请检查网络和服务地址")
        return
    
    # 询问是否进行生成测试
    print("\n" + "=" * 50)
    response = input("是否进行图像生成测试？(y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是']:
        generation_ok = await test_generation()
        
        if generation_ok:
            print("\n🎉 所有测试通过！ComfyUI云端算力可以正常使用")
        else:
            print("\n⚠️  连接正常但生成失败，请检查模型和参数")
    else:
        print("\n✅ 连接测试通过！")
    
    print("\n📝 接下来您可以:")
    print("1. 修改 comfyui_config.yaml 配置文件")
    print("2. 运行 python start_comfyui_node.py 启动算力节点")
    print("3. 将节点连接到您的Dreamify调度中心")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)
