import { Router, Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { TaskService } from '../services/TaskService';
import { NodeService } from '../services/NodeService';
import { logger } from '../utils/logger';
import { ApiError } from '../utils/ApiError';
import { asyncHandler } from '../utils/asyncHandler';

const router = Router();
const taskService = new TaskService();
const nodeService = new NodeService();

// 验证规则
const createTaskValidation = [
  body('prompt').isString().isLength({ min: 1, max: 1000 }).withMessage('Prompt must be 1-1000 characters'),
  body('model').isString().isIn(['stable-diffusion-xl', 'flux-dev', 'custom-model']).withMessage('Invalid model'),
  body('width').isInt({ min: 64, max: 1920 }).withMessage('Width must be between 64-1920'),
  body('height').isInt({ min: 64, max: 1920 }).withMessage('Height must be between 64-1920'),
  body('steps').isInt({ min: 1, max: 100 }).withMessage('Steps must be between 1-100'),
  body('batch_size').isInt({ min: 1, max: 4 }).withMessage('Batch size must be between 1-4'),
  body('cfg_scale').optional().isFloat({ min: 1, max: 20 }).withMessage('CFG scale must be between 1-20'),
  body('seed').optional().isInt({ min: -1 }).withMessage('Seed must be a positive integer or -1 for random'),
  body('negative_prompt').optional().isString().isLength({ max: 500 }).withMessage('Negative prompt max 500 characters')
];

const getTasksValidation = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1-100'),
  query('status').optional().isIn(['pending', 'processing', 'completed', 'failed']).withMessage('Invalid status'),
  query('model').optional().isString().withMessage('Model must be a string')
];

const taskIdValidation = [
  param('taskId').isUUID().withMessage('Task ID must be a valid UUID')
];

/**
 * @route POST /api/v1/tasks
 * @desc 创建新的图像生成任务
 * @access Private
 */
router.post('/', createTaskValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ApiError(400, 'Validation failed', errors.array());
  }

  const userId = (req as any).user.id;
  const taskData = {
    ...req.body,
    userId,
    status: 'pending',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // 检查用户是否有足够的配额
  const userQuota = await taskService.getUserQuota(userId);
  if (userQuota.remaining <= 0) {
    throw new ApiError(429, 'Quota exceeded. Please wait or upgrade your plan.');
  }

  // 检查是否有可用的算力节点
  const availableNodes = await nodeService.getAvailableNodes(req.body.model);
  if (availableNodes.length === 0) {
    throw new ApiError(503, 'No available compute nodes for the selected model');
  }

  // 创建任务
  const task = await taskService.createTask(taskData);

  // 将任务添加到队列
  await taskService.addToQueue(task);

  logger.info(`Task created: ${task.id} by user: ${userId}`);

  res.status(201).json({
    success: true,
    data: {
      taskId: task.id,
      status: task.status,
      estimatedTime: task.estimatedTime,
      queuePosition: task.queuePosition
    },
    message: 'Task created successfully'
  });
}));

/**
 * @route GET /api/v1/tasks
 * @desc 获取用户的任务列表
 * @access Private
 */
router.get('/', getTasksValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ApiError(400, 'Validation failed', errors.array());
  }

  const userId = (req as any).user.id;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const status = req.query.status as string;
  const model = req.query.model as string;

  const filters = {
    userId,
    ...(status && { status }),
    ...(model && { model })
  };

  const result = await taskService.getUserTasks(filters, page, limit);

  res.json({
    success: true,
    data: result.tasks,
    pagination: {
      page,
      limit,
      total: result.total,
      pages: Math.ceil(result.total / limit)
    }
  });
}));

/**
 * @route GET /api/v1/tasks/:taskId
 * @desc 获取特定任务的详细信息
 * @access Private
 */
router.get('/:taskId', taskIdValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ApiError(400, 'Validation failed', errors.array());
  }

  const userId = (req as any).user.id;
  const taskId = req.params.taskId;

  const task = await taskService.getTaskById(taskId, userId);
  if (!task) {
    throw new ApiError(404, 'Task not found');
  }

  res.json({
    success: true,
    data: task
  });
}));

/**
 * @route DELETE /api/v1/tasks/:taskId
 * @desc 取消任务（仅限待处理状态）
 * @access Private
 */
router.delete('/:taskId', taskIdValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ApiError(400, 'Validation failed', errors.array());
  }

  const userId = (req as any).user.id;
  const taskId = req.params.taskId;

  const task = await taskService.getTaskById(taskId, userId);
  if (!task) {
    throw new ApiError(404, 'Task not found');
  }

  if (task.status !== 'pending') {
    throw new ApiError(400, 'Can only cancel pending tasks');
  }

  await taskService.cancelTask(taskId);

  logger.info(`Task cancelled: ${taskId} by user: ${userId}`);

  res.json({
    success: true,
    message: 'Task cancelled successfully'
  });
}));

/**
 * @route GET /api/v1/tasks/:taskId/progress
 * @desc 获取任务进度（WebSocket替代方案）
 * @access Private
 */
router.get('/:taskId/progress', taskIdValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ApiError(400, 'Validation failed', errors.array());
  }

  const userId = (req as any).user.id;
  const taskId = req.params.taskId;

  const progress = await taskService.getTaskProgress(taskId, userId);
  if (!progress) {
    throw new ApiError(404, 'Task not found');
  }

  res.json({
    success: true,
    data: progress
  });
}));

/**
 * @route POST /api/v1/tasks/:taskId/retry
 * @desc 重试失败的任务
 * @access Private
 */
router.post('/:taskId/retry', taskIdValidation, asyncHandler(async (req: Request, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ApiError(400, 'Validation failed', errors.array());
  }

  const userId = (req as any).user.id;
  const taskId = req.params.taskId;

  const task = await taskService.getTaskById(taskId, userId);
  if (!task) {
    throw new ApiError(404, 'Task not found');
  }

  if (task.status !== 'failed') {
    throw new ApiError(400, 'Can only retry failed tasks');
  }

  await taskService.retryTask(taskId);

  logger.info(`Task retried: ${taskId} by user: ${userId}`);

  res.json({
    success: true,
    message: 'Task queued for retry'
  });
}));

export default router;
