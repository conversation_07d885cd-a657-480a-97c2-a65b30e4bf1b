# 数据库配置
DATABASE_URL=postgresql://dreamify:password@localhost:5432/dreamify
DB_HOST=localhost
DB_PORT=5432
DB_NAME=dreamify
DB_USER=dreamify
DB_PASSWORD=password

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# MinIO对象存储配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=dreamify-images
MINIO_USE_SSL=false

# API配置
API_PORT=5000
API_HOST=0.0.0.0
CORS_ORIGIN=http://localhost:3000

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_WS_URL=ws://localhost:5000

# AI服务配置
AI_SERVICE_URL=http://localhost:8000
AI_SERVICE_PORT=8000
MODEL_CACHE_DIR=./models
CUDA_VISIBLE_DEVICES=0

# 算力节点配置
SCHEDULER_URL=http://localhost:8001
NODE_REGISTRY_URL=http://localhost:8001/api/nodes
HEALTH_CHECK_INTERVAL=30
MAX_CONCURRENT_TASKS=4

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# 任务配置
DEFAULT_TASK_TIMEOUT=300  # 5分钟
MAX_QUEUE_SIZE=1000
TASK_RETRY_ATTEMPTS=3

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
LOG_LEVEL=info

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# 第三方服务配置
# OpenAI API（用于提示词优化）
OPENAI_API_KEY=your-openai-api-key

# 云存储配置（生产环境）
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET=dreamify-production

# 安全配置
RATE_LIMIT_WINDOW_MS=900000  # 15分钟
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_ROUNDS=12

# 缓存配置
CACHE_TTL=3600  # 1小时
IMAGE_CACHE_TTL=86400  # 24小时

# 算力节点特定配置
NODE_ID=node-001
NODE_NAME=RTX4090-Node-1
NODE_GPU_MODEL=RTX 4090
NODE_GPU_MEMORY=24576  # MB
NODE_MAX_BATCH_SIZE=4
NODE_SUPPORTED_MODELS=stable-diffusion-xl,flux-dev,custom-model

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30000
WS_CONNECTION_TIMEOUT=60000

# 开发环境配置
NODE_ENV=development
DEBUG=dreamify:*

# 生产环境配置
# NODE_ENV=production
# SSL_CERT_PATH=/etc/ssl/certs/dreamify.crt
# SSL_KEY_PATH=/etc/ssl/private/dreamify.key
